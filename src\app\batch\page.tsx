'use client';

import React, { useEffect, useState } from 'react';
import { createStyles } from 'antd-style';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import AppLayout from '@/components/layout/AppLayout';
import SimpleBatchGenerator from '@/components/batch/SimpleBatchGenerator';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100vh;
    background: ${token.colorBgLayout};
    overflow: hidden;
  `,
}));

const BatchPage: React.FC = () => {
  const { styles } = useStyles();
  const router = useRouter();
  const { isLoggedIn, checkLoginStatus } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  // 检查登录状态
  useEffect(() => {
    const initAuth = async () => {
      await checkLoginStatus();
      const currentState = useAuthStore.getState();
      if (!currentState.isLoggedIn) {
        router.replace('/login');
        return;
      }
      setMounted(true);
    };
    initAuth();
  }, [router, checkLoginStatus]);

  if (!mounted) {
    return <LoadingSpinner />;
  }

  return (
    <AppLayout>
      <div className={styles.container}>
        <SimpleBatchGenerator />
      </div>
    </AppLayout>
  );
};

export default BatchPage;