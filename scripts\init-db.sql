-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS story_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE story_ai;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    avatar VARCHAR(500),
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    verification_expires_at TIMESTAMP NULL,
    reset_token VARCHAR(255),
    reset_expires_at TIMESTAMP NULL,
    last_login_at TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    provider VARCHAR(50) DEFAULT 'local', -- local, wechat, qq, google
    provider_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_provider (provider, provider_id),
    INDEX idx_verification_token (verification_token),
    INDEX idx_reset_token (reset_token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建提示词表
CREATE TABLE IF NOT EXISTS prompts (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100) DEFAULT 'general',
    tags JSON,
    is_favorite BOOLEAN DEFAULT FALSE,
    usage_count INT DEFAULT 0,
    user_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_user_id (user_id),
    INDEX idx_is_favorite (is_favorite),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建智能体表
CREATE TABLE IF NOT EXISTS agents (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    avatar VARCHAR(500),
    category VARCHAR(100) DEFAULT 'general',
    tags JSON,
    system_prompt TEXT,
    model VARCHAR(100) DEFAULT 'gpt-3.5-turbo',
    temperature DECIMAL(3,2) DEFAULT 0.7,
    max_tokens INT DEFAULT 2000,
    is_favorite BOOLEAN DEFAULT FALSE,
    usage_count INT DEFAULT 0,
    user_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_user_id (user_id),
    INDEX idx_is_favorite (is_favorite),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建会话表
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    agent_id VARCHAR(50),
    user_id VARCHAR(50),
    messages JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例数据
INSERT IGNORE INTO prompts (id, title, content, category, tags, is_favorite, usage_count) VALUES
('prompt_1', '写作助手', '你是一个专业的写作助手，帮助用户改进文章结构和表达。', 'writing', '["写作", "助手"]', true, 15),
('prompt_2', '代码审查', '你是一个资深的代码审查专家，帮助发现代码中的问题并提供改进建议。', 'programming', '["代码", "审查"]', false, 8),
('prompt_3', '翻译专家', '你是一个专业的翻译专家，能够准确翻译各种语言。', 'translation', '["翻译", "语言"]', true, 22);

INSERT IGNORE INTO agents (id, name, description, avatar, category, tags, system_prompt, is_favorite, usage_count) VALUES
('agent_1', 'GPT-4 助手', '基于GPT-4的通用AI助手，能够处理各种任务', '/favicon.png', 'general', '["通用", "GPT-4"]', '你是一个有用的AI助手，请尽力帮助用户解决问题。', true, 45),
('agent_2', '代码专家', '专门用于编程和代码相关任务的AI助手', '/favicon.png', 'programming', '["编程", "代码"]', '你是一个专业的编程专家，精通多种编程语言和开发技术。', false, 23),
('agent_3', '创意写手', '专门用于创意写作和内容创作的AI助手', '/favicon.png', 'writing', '["写作", "创意"]', '你是一个富有创意的写手，能够创作各种类型的文章和故事。', true, 31);
