'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import PromptManager from '@/components/prompts/PromptManager';
import AppLayout from '@/components/layout/AppLayout';

export default function PromptsPage() {
  const router = useRouter();
  const { isLoggedIn, checkLoginStatus } = useAuthStore();

  useEffect(() => {
    const initAuth = async () => {
      await checkLoginStatus();

      if (!isLoggedIn) {
        router.replace('/login');
      }
    };

    initAuth();
  }, [isLoggedIn, router, checkLoginStatus]);

  if (!isLoggedIn) {
    return null;
  }

  return (
    <AppLayout>
      <PromptManager />
    </AppLayout>
  );
}