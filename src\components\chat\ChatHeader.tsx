'use client';

import React from 'react';
import { createStyles } from 'antd-style';
import { Button, Tooltip, App } from 'antd';
import { Trash2 } from 'lucide-react';
import Image from 'next/image';
import { useChatStore } from '@/store/chat';

const useStyles = createStyles(({ css, token }) => ({
  header: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgContainer};
    min-height: 60px;
  `,
  
  leftSection: css`
    display: flex;
    align-items: center;
    gap: 12px;
  `,
  
  logo: css`
    width: 32px;
    height: 32px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
  `,
  
  title: css`
    font-size: 18px;
    font-weight: 600;
    color: ${token.colorText};
    margin: 0;
  `,

  rightSection: css`
    display: flex;
    align-items: center;
    gap: 8px;
  `,

  clearButton: css`
    color: ${token.colorTextSecondary};
    border-color: ${token.colorBorderSecondary};

    &:hover {
      color: ${token.colorError};
      border-color: ${token.colorError};
    }
  `,

}));

interface ChatHeaderProps {
  title?: string;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  title = "微甜 AI Studio"
}) => {
  const { styles } = useStyles();
  const { currentSessions, activeSessionId, clearSessionMessages } = useChatStore();
  const { modal } = App.useApp();

  // 获取当前会话
  const currentSession = currentSessions.find(s => s.id === activeSessionId);
  const hasMessages = currentSession?.messages && currentSession.messages.length > 0;

  const handleClearMessages = () => {
    modal.confirm({
      title: '清空对话',
      content: '确定要清空当前对话的所有消息吗？此操作不可撤销。',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        clearSessionMessages();
      },
    });
  };

  return (
    <div className={styles.header}>
      <div className={styles.leftSection}>
        <div className={styles.logo}>
          <Image
            src="/favicon.png"
            alt="Logo"
            width={32}
            height={32}
            style={{ objectFit: 'cover' }}
          />
        </div>
        <h1 className={styles.title}>{title}</h1>
      </div>

      <div className={styles.rightSection}>
        {hasMessages && (
          <Tooltip title="清空对话">
            <Button
              type="text"
              icon={<Trash2 size={16} />}
              className={styles.clearButton}
              onClick={handleClearMessages}
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default ChatHeader;
