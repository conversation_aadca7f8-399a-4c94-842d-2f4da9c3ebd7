import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// GET - 查看数据库中的原始数据
export async function GET(request: NextRequest) {
  try {
    console.log('查看数据库中的原始preset_prompts数据...');

    // 直接查看原始数据
    const rawQuery = `
      SELECT id, name, preset_prompts
      FROM agents 
      WHERE preset_prompts IS NOT NULL 
        AND preset_prompts != 'null'
        AND preset_prompts != ''
      ORDER BY id
    `;

    const rawData = await executeQuery<any[]>(rawQuery);
    console.log('原始数据库数据:');
    
    rawData.forEach(row => {
      console.log(`智能体 ${row.id} (${row.name}):`);
      console.log(`  原始数据: ${row.preset_prompts}`);
      console.log(`  数据类型: ${typeof row.preset_prompts}`);
      console.log(`  字符长度: ${row.preset_prompts.length}`);
      
      // 尝试解析
      try {
        const parsed = JSON.parse(row.preset_prompts);
        console.log(`  解析成功: ${JSON.stringify(parsed)}`);
      } catch (error) {
        console.log(`  解析失败: ${error instanceof Error ? error.message : String(error)}`);
      }
      console.log('---');
    });

    return NextResponse.json({
      success: true,
      message: '原始数据检查完成',
      data: rawData.map(row => ({
        id: row.id,
        name: row.name,
        preset_prompts: row.preset_prompts,
        type: typeof row.preset_prompts,
        length: row.preset_prompts.length
      }))
    });
  } catch (error) {
    console.error('检查原始数据失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '检查原始数据失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
