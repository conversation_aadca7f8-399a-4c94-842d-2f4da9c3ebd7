import { NextRequest, NextResponse } from 'next/server';
import { AIProvider } from '@/types';

// 测试AI提供商连接
export async function POST(request: NextRequest) {
  let requestBody;

  try {
    // 只读取一次请求体
    requestBody = await request.json();
  } catch (error) {
    console.error('解析请求体失败:', error);
    return NextResponse.json(
      { success: false, error: '请求格式错误' },
      { status: 400 }
    );
  }

  try {
    const { provider, apiKey, baseUrl, model } = requestBody;

    // 添加详细日志
    console.log('=== API 测试请求详情 ===');
    console.log('Provider:', provider);
    console.log('API Key (前10字符):', apiKey?.substring(0, 10) + '...');
    console.log('Base URL:', baseUrl);
    console.log('Model:', model);
    console.log('========================');

    if (!provider || !apiKey) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 根据不同的AI提供商调用相应的API
    const result = await testProviderConnection(provider, apiKey, baseUrl, model);

    return NextResponse.json(result);
  } catch (error) {
    console.error('AI连接测试失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      },
      { status: 500 }
    );
  }
}

async function testProviderConnection(
  provider: AIProvider, 
  apiKey: string, 
  baseUrl?: string, 
  model?: string
): Promise<{ success: boolean; error?: string; data?: any }> {
  try {
    switch (provider) {
      case 'openai':
        return await testOpenAIConnection(apiKey, baseUrl, model);
      case 'anthropic':
        return await testAnthropicConnection(apiKey, baseUrl, model);
      case 'google':
        return await testGoogleConnection(apiKey, baseUrl, model);
      case 'deepseek':
        return await testDeepSeekConnection(apiKey, baseUrl, model);
      default:
        return { success: false, error: '不支持的AI提供商' };
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '连接测试失败' 
    };
  }
}

async function testOpenAIConnection(apiKey: string, baseUrl?: string, model?: string) {
  // 处理 baseUrl，确保格式正确
  let apiBaseUrl = baseUrl?.trim() || 'https://api.openai.com/v1';

  // 移除末尾的斜杠
  if (apiBaseUrl.endsWith('/')) {
    apiBaseUrl = apiBaseUrl.slice(0, -1);
  }

  // 确保 URL 格式正确
  if (!apiBaseUrl.startsWith('http://') && !apiBaseUrl.startsWith('https://')) {
    apiBaseUrl = 'https://' + apiBaseUrl;
  }

  // 如果 baseUrl 已经包含 /v1，就直接添加 /models，否则添加 /v1/models
  const url = apiBaseUrl.includes('/v1') ? `${apiBaseUrl}/models` : `${apiBaseUrl}/v1/models`;

  console.log('=== OpenAI 请求详情 ===');
  console.log('URL:', url);
  console.log('API Key 长度:', apiKey.length);
  console.log('API Key 前缀:', apiKey.substring(0, 7));
  console.log('Headers:', {
    'Authorization': `Bearer ${apiKey.substring(0, 10)}...`,
    'Content-Type': 'application/json',
  });
  console.log('=====================');

  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  console.log('Response status:', response.status);
  console.log('Response headers:', Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const responseText = await response.text();
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.error?.message || errorData.message || errorMessage;
      } catch {
        errorMessage = responseText || errorMessage;
      }
    } catch {
      // 如果无法读取响应体，使用默认错误消息
    }
    throw new Error(`OpenAI API错误: ${errorMessage}`);
  }

  const data = await response.json();
  return {
    success: true,
    data: {
      message: 'OpenAI连接成功',
      modelsCount: data.data?.length || 0
    }
  };
}

async function testAnthropicConnection(apiKey: string, baseUrl?: string, model?: string) {
  // 处理 baseUrl，确保格式正确
  let apiBaseUrl = baseUrl?.trim() || 'https://api.anthropic.com';

  // 移除末尾的斜杠
  if (apiBaseUrl.endsWith('/')) {
    apiBaseUrl = apiBaseUrl.slice(0, -1);
  }

  // 确保 URL 格式正确
  if (!apiBaseUrl.startsWith('http://') && !apiBaseUrl.startsWith('https://')) {
    apiBaseUrl = 'https://' + apiBaseUrl;
  }

  // 智能处理 URL 路径，兼容标准 API 和代理 API
  let finalUrl;
  let isProxyAPI = false;

  if (apiBaseUrl.includes('/v1') && !apiBaseUrl.includes('/v1/messages')) {
    // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
    finalUrl = `${apiBaseUrl}/chat/completions`;
    isProxyAPI = true;
  } else {
    // 标准 Anthropic API 格式
    finalUrl = `${apiBaseUrl}/v1/messages`;
  }

  console.log('Testing Anthropic connection to:', finalUrl);

  // 根据 API 类型选择不同的请求体格式
  let requestBody;
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    requestBody = {
      model: model || 'claude-3-haiku-20240307',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 10,
    };
    headers['Authorization'] = `Bearer ${apiKey}`;
  } else {
    // 标准 Anthropic API 格式
    requestBody = {
      model: model || 'claude-3-haiku-20240307',
      max_tokens: 10,
      messages: [{ role: 'user', content: 'Hi' }]
    };
    headers['Authorization'] = `Bearer ${apiKey}`;
    headers['anthropic-version'] = '2023-06-01';
  }

  const response = await fetch(finalUrl, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const responseText = await response.text();
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.error?.message || errorData.message || errorMessage;
      } catch {
        errorMessage = responseText || errorMessage;
      }
    } catch {
      // 如果无法读取响应体，使用默认错误消息
    }
    throw new Error(`Anthropic API错误: ${errorMessage}`);
  }

  return {
    success: true,
    data: { message: 'Anthropic连接成功' }
  };
}

async function testGoogleConnection(apiKey: string, baseUrl?: string, model?: string) {
  // 处理 baseUrl，确保格式正确
  let apiBaseUrl = baseUrl?.trim() || 'https://generativelanguage.googleapis.com';

  // 移除末尾的斜杠
  if (apiBaseUrl.endsWith('/')) {
    apiBaseUrl = apiBaseUrl.slice(0, -1);
  }

  // 确保 URL 格式正确
  if (!apiBaseUrl.startsWith('http://') && !apiBaseUrl.startsWith('https://')) {
    apiBaseUrl = 'https://' + apiBaseUrl;
  }

  // 智能处理 URL 路径，兼容标准 API 和代理 API
  let finalUrl;
  if (apiBaseUrl.includes('/v1beta')) {
    // 如果已经包含 v1beta 路径，直接添加 models 部分
    finalUrl = `${apiBaseUrl}/models/${model || 'gemini-2.0-flash'}:generateContent?key=${apiKey}`;
  } else if (apiBaseUrl.includes('/v1') && !apiBaseUrl.includes('/v1beta')) {
    // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
    finalUrl = `${apiBaseUrl}/chat/completions`;
  } else {
    // 标准情况，添加完整的 v1beta/models 路径
    finalUrl = `${apiBaseUrl}/v1beta/models/${model || 'gemini-2.0-flash'}:generateContent?key=${apiKey}`;
  }

  const url = finalUrl;

  console.log('Testing Google connection to:', url);

  // 根据 URL 格式选择不同的请求体
  let requestBody;
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (apiBaseUrl.includes('/v1') && !apiBaseUrl.includes('/v1beta')) {
    // 代理 API 使用 OpenAI 兼容格式
    requestBody = {
      model: model || 'gemini-2.0-flash',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 10,
    };
    headers['Authorization'] = `Bearer ${apiKey}`;
  } else {
    // 标准 Google API 格式
    requestBody = {
      contents: [{
        parts: [{ text: 'Hi' }]
      }],
      generationConfig: {
        maxOutputTokens: 10,
      },
    };
  }

  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const responseText = await response.text();
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.error?.message || errorData.message || errorMessage;
      } catch {
        errorMessage = responseText || errorMessage;
      }
    } catch {
      // 如果无法读取响应体，使用默认错误消息
    }
    throw new Error(`Google API错误: ${errorMessage}`);
  }

  return {
    success: true,
    data: { message: 'Google Gemini连接成功' }
  };
}

async function testDeepSeekConnection(apiKey: string, baseUrl?: string, model?: string) {
  // 处理 baseUrl，确保格式正确
  let apiBaseUrl = baseUrl?.trim() || 'https://api.deepseek.com';

  // 移除末尾的斜杠
  if (apiBaseUrl.endsWith('/')) {
    apiBaseUrl = apiBaseUrl.slice(0, -1);
  }

  // 确保 URL 格式正确
  if (!apiBaseUrl.startsWith('http://') && !apiBaseUrl.startsWith('https://')) {
    apiBaseUrl = 'https://' + apiBaseUrl;
  }

  const url = `${apiBaseUrl}/v1/chat/completions`;

  console.log('Testing DeepSeek connection to:', url);

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: model || 'deepseek-chat',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 10,
    }),
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const responseText = await response.text();
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.error?.message || errorData.message || errorMessage;
      } catch {
        errorMessage = responseText || errorMessage;
      }
    } catch {
      // 如果无法读取响应体，使用默认错误消息
    }
    throw new Error(`DeepSeek API错误: ${errorMessage}`);
  }

  return {
    success: true,
    data: { message: 'DeepSeek连接成功' }
  };
}
