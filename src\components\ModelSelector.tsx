import React, { useMemo, useEffect } from 'react';
import { Select, Tag, Tooltip, Space, Typography } from 'antd';
import { RobotOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { useAuthStore } from '@/store/auth';
import { AIProvider, AI_PROVIDERS } from '@/types/ai-provider';

const { Text } = Typography;
const { Option, OptGroup } = Select;

interface ModelSelectorProps {
  value?: { provider: AIProvider; model: string };
  onChange?: (provider: AIProvider, model: string) => void;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  className?: string;
  placeholder?: string;
}

const providerIcons = {
  openai: '🤖',
  anthropic: '🧠',
  google: '🔍',
  deepseek: '🚀',
};

const providerColors = {
  openai: '#10a37f',
  anthropic: '#d97706',
  google: '#4285f4',
  deepseek: '#722ed1',
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange,
  size = 'middle',
  style,
  className,
  placeholder = '选择AI模型',
}) => {
  const { user } = useAuthStore();
  const {
    configs,
    userSettings,
    loadConfigs,
    loadUserSettings,
    updateCurrentModel,
    getProviderConfig,
    isProviderEnabled,
  } = useAIProviderConfigsStore();

  // 加载配置
  useEffect(() => {
    if (user?.id) {
      loadConfigs(user.id);
      loadUserSettings(user.id);
    }
  }, [user?.id, loadConfigs, loadUserSettings]);

  // 获取启用的提供商配置
  const enabledProviders = useMemo(() => {
    return Object.entries(AI_PROVIDERS).filter(([providerId]) =>
      isProviderEnabled(providerId as AIProvider)
    );
  }, [configs, isProviderEnabled]);

  const options = useMemo(() => {
    return enabledProviders.map(([providerId, providerInfo]) => ({
      label: (
        <Space>
          <span>{providerIcons[providerId as AIProvider]}</span>
          <Text strong style={{ color: providerColors[providerId as AIProvider] }}>
            {providerInfo.name}
          </Text>
        </Space>
      ),
      options: providerInfo.models.map((model) => ({
        label: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <div style={{ fontWeight: 500 }}>{model.name}</div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {model.description}
              </Text>
            </div>
            <div style={{ display: 'flex', gap: 4, alignItems: 'center' }}>
              {model.supportFiles && (
                <Tooltip title="支持文件上传">
                  <Tag color="blue">📎</Tag>
                </Tooltip>
              )}
              <Tooltip title={`最大令牌: ${model.maxTokens.toLocaleString()}`}>
                <Tag color="green">
                  {model.maxTokens >= 1000000
                    ? `${(model.maxTokens / 1000000).toFixed(1)}M`
                    : `${(model.maxTokens / 1000).toFixed(0)}K`
                  }
                </Tag>
              </Tooltip>
              {model.inputPrice && (
                <Tooltip title={`输入: $${model.inputPrice}/1K, 输出: $${model.outputPrice}/1K`}>
                  <Tag color="orange">💰</Tag>
                </Tooltip>
              )}
            </div>
          </div>
        ),
        value: `${providerId}/${model.id}`,
        provider: providerId,
        model: model.id,
      })),
    }));
  }, [enabledProviders]);

  const handleChange = (selectedValue: string, option: any) => {
    const { provider, model } = option;

    // 更新数据库中的用户设置
    if (user?.id) {
      updateCurrentModel(provider, model);
    }

    // 调用外部onChange回调
    onChange?.(provider, model);
  };

  const currentValue = value
    ? `${value.provider}/${value.model}`
    : userSettings
      ? `${userSettings.currentProvider}/${userSettings.currentModel}`
      : '';

  // 如果没有启用的提供商，显示提示
  if (enabledProviders.length === 0) {
    return (
      <Select
        placeholder="请先在设置中配置AI提供商"
        disabled
        size={size}
        style={style}
        className={className}
        suffixIcon={<RobotOutlined />}
      />
    );
  }

  return (
    <Select
      value={currentValue}
      onChange={handleChange}
      placeholder={placeholder}
      size={size}
      style={style}
      className={className}
      optionLabelProp="label"
      suffixIcon={<ThunderboltOutlined />}
      showSearch
      filterOption={(input, option) => {
        const searchText = input.toLowerCase();
        const optionText = option?.children?.props?.children?.[0]?.props?.children?.[0]?.toLowerCase() || '';
        const providerText = option?.provider?.toLowerCase() || '';
        return optionText.includes(searchText) || providerText.includes(searchText);
      }}
    >
      {options.map((group) => (
        <OptGroup key={group.label.key} label={group.label}>
          {group.options.map((option) => (
            <Option
              key={option.value}
              value={option.value}
              provider={option.provider}
              model={option.model}
            >
              {option.label}
            </Option>
          ))}
        </OptGroup>
      ))}
    </Select>
  );
};

// 简化版本的模型选择器，只显示模型名称
export const SimpleModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange,
  size = 'small',
  style,
  className,
  placeholder = '选择模型',
}) => {
  const { user } = useAuthStore();
  const {
    configs,
    userSettings,
    loadConfigs,
    loadUserSettings,
    updateCurrentModel,
    isProviderEnabled,
  } = useAIProviderConfigsStore();

  // 加载配置
  useEffect(() => {
    if (user?.id) {
      loadConfigs(user.id);
      loadUserSettings(user.id);
    }
  }, [user?.id, loadConfigs, loadUserSettings]);

  // 获取启用的提供商配置
  const enabledProviders = useMemo(() => {
    return Object.entries(AI_PROVIDERS).filter(([providerId]) =>
      isProviderEnabled(providerId as AIProvider)
    );
  }, [configs, isProviderEnabled]);

  const options = useMemo(() => {
    const allModels: Array<{
      label: React.ReactNode;
      value: string;
      provider: AIProvider;
      model: string;
    }> = [];

    enabledProviders.forEach(([providerId, providerInfo]) => {
      providerInfo.models.forEach((model) => {
        allModels.push({
          label: (
            <Space>
              <span>{providerIcons[providerId as AIProvider]}</span>
              <Text>{model.name}</Text>
            </Space>
          ),
          value: `${providerId}/${model.id}`,
          provider: providerId as AIProvider,
          model: model.id,
        });
      });
    });

    return allModels;
  }, [enabledProviders]);

  const handleChange = (selectedValue: string, option: any) => {
    const { provider, model } = option;

    // 更新数据库中的用户设置
    if (user?.id) {
      updateCurrentModel(provider, model);
    }

    onChange?.(provider, model);
  };

  const currentValue = value
    ? `${value.provider}/${value.model}`
    : userSettings
      ? `${userSettings.currentProvider}/${userSettings.currentModel}`
      : '';

  if (enabledProviders.length === 0) {
    return (
      <Select
        placeholder="请先配置AI提供商"
        disabled
        size={size}
        style={style}
        className={className}
      />
    );
  }

  return (
    <Select
      value={currentValue}
      onChange={handleChange}
      placeholder={placeholder}
      size={size}
      style={style}
      className={className}
      showSearch
      filterOption={(input, option) => {
        const searchText = input.toLowerCase();
        const optionText = option?.children?.props?.children?.[1]?.props?.children?.toLowerCase() || '';
        return optionText.includes(searchText);
      }}
    >
      {options.map((option) => (
        <Option
          key={option.value}
          value={option.value}
          provider={option.provider}
          model={option.model}
        >
          {option.label}
        </Option>
      ))}
    </Select>
  );
};
