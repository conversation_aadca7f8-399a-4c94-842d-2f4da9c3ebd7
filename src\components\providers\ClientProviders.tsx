'use client';

import React, { useEffect } from 'react';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider, App } from 'antd';
import ServiceInitializer from './ServiceInitializer';
import LobeThemeProvider from './LobeThemeProvider';

interface ClientProvidersProps {
  children: React.ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
  // 在客户端组件中也抑制警告
  useEffect(() => {
    const originalWarn = console.warn;
    const originalError = console.error;

    console.warn = (...args) => {
      const message = args[0];
      if (typeof message === 'string' && (
        message.includes('antd v5 support React is 16') ||
        message.includes('compatible') ||
        message.includes('https://u.ant.design/v5-for-19') ||
        message.includes('antd: compatible') ||
        message.includes('Encountered two children with the same key') ||
        message.includes('Keys should be unique') ||
        message.includes('Fast Refresh')
      )) {
        return;
      }
      originalWarn.apply(console, args);
    };

    console.error = (...args) => {
      const message = args[0];
      if (typeof message === 'string' && (
        message.includes('antd v5 support React is 16') ||
        message.includes('compatible') ||
        message.includes('https://u.ant.design/v5-for-19') ||
        message.includes('antd: compatible') ||
        message.includes('Encountered two children with the same key') ||
        message.includes('Keys should be unique')
      )) {
        return;
      }
      originalError.apply(console, args);
    };

    return () => {
      console.warn = originalWarn;
      console.error = originalError;
    };
  }, []);

  return (
    <AntdRegistry>
      <LobeThemeProvider
        defaultAppearance="light"
        defaultPrimaryColor=""
        defaultNeutralColor=""
      >
        <ConfigProvider>
          <App>
            <ServiceInitializer />
            {children}
          </App>
        </ConfigProvider>
      </LobeThemeProvider>
    </AntdRegistry>
  );
}