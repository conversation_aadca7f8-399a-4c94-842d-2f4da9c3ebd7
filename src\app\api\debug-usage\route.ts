import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// GET - 调试查询使用记录
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    let userId = searchParams.get('userId');
    let agentId = searchParams.get('agentId');

    // 如果没有提供参数，查询所有用户和智能体
    if (!userId || !agentId) {
      // 查询所有用户
      const usersQuery = `SELECT id, username FROM users LIMIT 10`;
      const users = await executeQuery<any[]>(usersQuery);

      // 查询所有智能体
      const agentsQuery = `SELECT id, name FROM agents LIMIT 10`;
      const agents = await executeQuery<any[]>(agentsQuery);

      // 查询所有使用记录
      const allRecordsQuery = `
        SELECT
          user_id,
          agent_id,
          COUNT(*) as record_count,
          SUM(usage_count) as total_usage
        FROM agent_usage_logs
        GROUP BY user_id, agent_id
        ORDER BY total_usage DESC
        LIMIT 10
      `;
      const allRecords = await executeQuery<any[]>(allRecordsQuery);

      return NextResponse.json({
        success: true,
        data: {
          users,
          agents,
          usageByUserAgent: allRecords,
        },
      });
    }

    // 查询最近的使用记录
    const query = `
      SELECT 
        id,
        user_id,
        agent_id,
        session_id,
        usage_type,
        usage_count,
        remaining_count,
        created_at
      FROM agent_usage_logs
      WHERE user_id = ? AND agent_id = ?
      ORDER BY created_at DESC
      LIMIT 20
    `;

    const records = await executeQuery<any[]>(query, [userId, agentId]);

    // 查询总使用次数
    const totalQuery = `
      SELECT 
        COUNT(*) as total_records,
        SUM(usage_count) as total_usage_count
      FROM agent_usage_logs
      WHERE user_id = ? AND agent_id = ?
    `;

    const totalResult = await executeQuery<any[]>(totalQuery, [userId, agentId]);
    const total = totalResult[0] || { total_records: 0, total_usage_count: 0 };

    // 查询智能体的试用次数
    const agentQuery = `
      SELECT trial_usage_count 
      FROM agents 
      WHERE id = ? 
      LIMIT 1
    `;

    const agentResult = await executeQuery<any[]>(agentQuery, [agentId]);
    const trialCount = agentResult.length > 0 ? (agentResult[0].trial_usage_count || 3) : 3;

    return NextResponse.json({
      success: true,
      data: {
        records,
        summary: {
          totalRecords: total.total_records,
          totalUsageCount: total.total_usage_count,
          trialCount,
          remainingCount: Math.max(0, trialCount - total.total_usage_count),
        },
      },
    });

  } catch (error) {
    console.error('查询使用记录失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '查询使用记录失败',
      },
      { status: 500 }
    );
  }
}
