<!DOCTYPE html>
<html>
<head>
    <title>测试使用次数累加</title>
</head>
<body>
    <h1>测试使用次数累加功能</h1>
    <button onclick="testUsage()">测试使用次数记录</button>
    <button onclick="queryUsage()">查询使用统计</button>
    <div id="result"></div>

    <script>
        async function testUsage() {
            const testData = {
                userId: 'mdoi3uqorptmrc7zwr',
                agentId: 'mdpk4ezijzy5fxs26v',
                sessionId: 'test_session_' + Date.now(),
                usageType: 'chat',
                tokensUsed: 0,
                cost: 0,
                usageCount: 1,
                expiryDate: null
            };

            try {
                console.log('发送测试数据:', testData);
                
                const response = await fetch('/api/agent-usage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                });

                const result = await response.json();
                console.log('API响应:', result);
                
                document.getElementById('result').innerHTML = '<h3>API响应:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                
            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('result').innerHTML = '错误: ' + error.message;
            }
        }

        async function queryUsage() {
            try {
                const response = await fetch('/api/agent-usage?userId=mdoi3uqorptmrc7zwr&agentId=mdpk4ezijzy5fxs26v');
                const result = await response.json();
                console.log('查询结果:', result);
                
                document.getElementById('result').innerHTML = '<h3>查询结果:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                
            } catch (error) {
                console.error('查询失败:', error);
                document.getElementById('result').innerHTML = '查询错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
