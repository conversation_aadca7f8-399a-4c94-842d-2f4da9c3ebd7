// Entry component
import { App } from 'antd';
import type { MessageInstance } from 'antd/es/message/interface';
import type { ModalStaticFunctions } from 'antd/es/modal/confirm';
import type { NotificationInstance } from 'antd/es/notification/interface';
import { memo } from 'react';

let messageInstance: MessageInstance;
let notificationInstance: NotificationInstance;
let modalInstance: Omit<ModalStaticFunctions, 'warn'>;

const AntdStaticMethods = memo(() => {
  const staticFunction = App.useApp();
  messageInstance = staticFunction.message;
  modalInstance = staticFunction.modal;
  notificationInstance = staticFunction.notification;

  // 调试信息
  console.log('AntdStaticMethods initialized:', {
    message: !!messageInstance,
    modal: !!modalInstance,
    notification: !!notificationInstance
  });

  return null;
});

// 导出安全的包装器
export const message = {
  success: (content: any, duration?: number, onClose?: () => void) => messageInstance?.success?.(content, duration, onClose),
  error: (content: any, duration?: number, onClose?: () => void) => messageInstance?.error?.(content, duration, onClose),
  info: (content: any, duration?: number, onClose?: () => void) => messageInstance?.info?.(content, duration, onClose),
  warning: (content: any, duration?: number, onClose?: () => void) => messageInstance?.warning?.(content, duration, onClose),
  loading: (content: any, duration?: number, onClose?: () => void) => messageInstance?.loading?.(content, duration, onClose),
  destroy: (key?: string) => messageInstance?.destroy?.(key),
};

export const modal = {
  confirm: (config: any) => modalInstance?.confirm?.(config),
  info: (config: any) => modalInstance?.info?.(config),
  success: (config: any) => modalInstance?.success?.(config),
  error: (config: any) => modalInstance?.error?.(config),
  warning: (config: any) => modalInstance?.warning?.(config),
};

export const notification = {
  success: (config: any) => notificationInstance?.success?.(config),
  error: (config: any) => notificationInstance?.error?.(config),
  info: (config: any) => notificationInstance?.info?.(config),
  warning: (config: any) => notificationInstance?.warning?.(config),
  open: (config: any) => notificationInstance?.open?.(config),
  destroy: (key?: string) => notificationInstance?.destroy?.(key),
};

AntdStaticMethods.displayName = 'AntdStaticMethods';

export default AntdStaticMethods;
