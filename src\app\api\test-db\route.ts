import { NextRequest, NextResponse } from 'next/server';
import { testConnection, executeQuery } from '@/lib/db';

// GET - 测试数据库连接和AI提供商配置
export async function GET(request: NextRequest) {
  try {
    console.log('=== 数据库测试开始 ===');

    // 1. 测试基本连接
    const isConnected = await testConnection();
    console.log('数据库连接测试:', isConnected);

    if (!isConnected) {
      return NextResponse.json({
        success: false,
        error: '数据库连接失败',
      }, { status: 500 });
    }

    // 2. 测试查询ai_provider_configs表
    console.log('测试查询ai_provider_configs表...');
    const configs = await executeQuery<any[]>(
      'SELECT * FROM ai_provider_configs LIMIT 5'
    );
    console.log('查询结果:', configs);

    // 3. 测试用户ID查询
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'test-user-id';
    console.log('测试用户ID查询:', userId);

    const userConfigs = await executeQuery<any[]>(
      'SELECT * FROM ai_provider_configs WHERE user_id = ?',
      [userId]
    );
    console.log('用户配置查询结果:', userConfigs);

    console.log('=== 数据库测试完成 ===');

    return NextResponse.json({
      success: true,
      data: {
        connectionTest: true,
        totalConfigs: configs.length,
        userConfigs: userConfigs.length,
        sampleConfigs: configs.map(config => ({
          id: config.id,
          userId: config.user_id,
          provider: config.provider,
          enabled: config.enabled
        }))
      }
    });

  } catch (error) {
    console.error('数据库测试失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '数据库测试失败',
      details: {
        errorType: error?.constructor?.name,
        errorMessage: error instanceof Error ? error.message : String(error)
      }
    }, { status: 500 });
  }
}
