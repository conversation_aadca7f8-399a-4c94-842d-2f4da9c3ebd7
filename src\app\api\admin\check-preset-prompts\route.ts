import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// GET - 检查preset_prompts字段的实际数据
export async function GET(request: NextRequest) {
  try {
    console.log('检查preset_prompts字段的实际数据...');

    // 查看所有非空的preset_prompts数据
    const dataQuery = `
      SELECT id, name, preset_prompts,
             JSON_VALID(preset_prompts) as is_valid_json,
             LENGTH(preset_prompts) as data_length,
             HEX(preset_prompts) as hex_data
      FROM agents 
      WHERE preset_prompts IS NOT NULL 
        AND preset_prompts != 'null'
        AND preset_prompts != ''
      ORDER BY id
    `;

    const data = await executeQuery<any[]>(dataQuery);
    console.log('找到的数据:', data.length, '条');
    
    data.forEach(row => {
      console.log(`智能体 ${row.id} (${row.name}):`);
      console.log(`  数据: ${row.preset_prompts}`);
      console.log(`  长度: ${row.data_length}`);
      console.log(`  有效JSON: ${row.is_valid_json === 1 ? '是' : '否'}`);
      console.log(`  十六进制: ${row.hex_data}`);
      console.log('---');
    });

    return NextResponse.json({
      success: true,
      message: 'preset_prompts数据检查完成',
      data: data.map(row => ({
        id: row.id,
        name: row.name,
        preset_prompts: row.preset_prompts,
        is_valid_json: row.is_valid_json === 1,
        data_length: row.data_length,
        hex_data: row.hex_data
      }))
    });
  } catch (error) {
    console.error('检查preset_prompts数据失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '检查preset_prompts数据失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
