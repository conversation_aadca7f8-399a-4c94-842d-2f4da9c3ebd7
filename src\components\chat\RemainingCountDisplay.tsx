'use client';

import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Tag, Tooltip, Button } from 'antd';
import { InfoCircleOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { createStyles } from 'antd-style';
import { agentRemainingApi } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { useChatStore } from '@/store/chat';

const useStyles = createStyles(({ token }) => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '4px 8px',
    borderRadius: '6px',
    backgroundColor: token.colorBgContainer,
    border: `1px solid ${token.colorBorder}`,
    fontSize: '12px',
  },
  
  subscriptionTag: {
    margin: 0,
    fontSize: '12px',
  },
  
  trialTag: {
    margin: 0,
    fontSize: '12px',
  },
  
  exhaustedTag: {
    margin: 0,
    fontSize: '12px',
  },
  
  upgradeButton: {
    height: '24px',
    fontSize: '12px',
    padding: '0 8px',
  },
}));

interface RemainingCountDisplayProps {
  agentId: string;
  className?: string;
}

export interface RemainingCountDisplayRef {
  refresh: () => void;
}

interface RemainingData {
  agentId: string;
  agentName: string;
  hasSubscription: boolean;
  subscriptionType?: 'time_based' | 'usage_based';
  remainingCount: number | null;
  expiryDate?: string;
  trialCount: number;
  usedCount?: number;
  status: 'subscribed' | 'trial_available' | 'trial_exhausted';
}

const RemainingCountDisplay = forwardRef<RemainingCountDisplayRef, RemainingCountDisplayProps>(
  ({ agentId, className }, ref) => {
    const { styles } = useStyles();
    const [remainingData, setRemainingData] = useState<RemainingData | null>(null);
    const [loading, setLoading] = useState(false);
    const { user } = useAuthStore();
    const { isLoading, currentSessions, activeSessionId } = useChatStore();

    useEffect(() => {
      if (user && agentId) {
        fetchRemainingCount();
      }
    }, [user, agentId]);

    // 监听消息发送状态，当发送完成后刷新剩余次数
    useEffect(() => {
      // 当 isLoading 从 true 变为 false 时，说明消息发送完成
      if (!isLoading && user && agentId) {
        // 延迟一点时间再刷新，确保后端数据已更新
        const timer = setTimeout(() => {
          fetchRemainingCount();
        }, 500);

        return () => clearTimeout(timer);
      }
    }, [isLoading, user, agentId]);

    const fetchRemainingCount = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const result = await agentRemainingApi.getRemainingCount(user.id, agentId);
        if (result.success && result.data) {
          setRemainingData(result.data);
        }
      } catch (error) {
        console.error('获取剩余次数失败:', error);
      } finally {
        setLoading(false);
      }
    };

    // 暴露刷新方法给父组件
    useImperativeHandle(ref, () => ({
      refresh: fetchRemainingCount
    }));

  const handleUpgrade = () => {
    // TODO: 跳转到订阅页面
    console.log('跳转到订阅页面');
  };

  if (loading || !remainingData) {
    return null;
  }

  const { hasSubscription, remainingCount, status, trialCount, usedCount, subscriptionType, expiryDate } = remainingData;

  // 已订阅用户
  if (hasSubscription) {
    if (subscriptionType === 'time_based') {
      const expiryDateObj = expiryDate ? new Date(expiryDate) : null;
      const daysLeft = expiryDateObj ? Math.ceil((expiryDateObj.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0;
      
      return (
        <div className={`${styles.container} ${className}`}>
          <Tag color="green" className={styles.subscriptionTag}>
            已订阅
          </Tag>
          <Tooltip title={`订阅到期时间：${expiryDateObj?.toLocaleDateString()}`}>
            <span>剩余 {daysLeft} 天</span>
          </Tooltip>
        </div>
      );
    } else {
      return (
        <div className={`${styles.container} ${className}`}>
          <Tag color="green" className={styles.subscriptionTag}>
            已订阅
          </Tag>
          <span>剩余 {remainingCount || '无限'} 次</span>
        </div>
      );
    }
  }

  // 试用用户
  if (status === 'trial_available') {
    const color = remainingCount && remainingCount <= 1 ? 'red' : remainingCount && remainingCount <= 3 ? 'orange' : 'blue';
    
    return (
      <div className={`${styles.container} ${className}`}>
        <Tag color={color} className={styles.trialTag}>
          试用
        </Tag>
        <span>剩余 {remainingCount} / {trialCount} 次</span>
        <Tooltip title="试用次数用完后需要购买订阅">
          <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
        </Tooltip>
        {remainingCount && remainingCount <= 3 && (
          <Button 
            type="primary" 
            size="small" 
            icon={<ShoppingCartOutlined />}
            className={styles.upgradeButton}
            onClick={handleUpgrade}
          >
            升级
          </Button>
        )}
      </div>
    );
  }

  // 试用已用完
  if (status === 'trial_exhausted') {
    return (
      <div className={`${styles.container} ${className}`}>
        <Tag color="red" className={styles.exhaustedTag}>
          试用已用完
        </Tag>
        <span>{usedCount} / {trialCount} 次</span>
        <Button 
          type="primary" 
          size="small" 
          icon={<ShoppingCartOutlined />}
          className={styles.upgradeButton}
          onClick={handleUpgrade}
        >
          立即购买
        </Button>
      </div>
    );
  }

    return null;
  }
);

RemainingCountDisplay.displayName = 'RemainingCountDisplay';

export default RemainingCountDisplay;
