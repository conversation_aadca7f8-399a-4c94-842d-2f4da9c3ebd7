import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// POST - 清理重复的使用记录，每个用户+智能体只保留一条记录
export async function POST(request: NextRequest) {
  try {
    console.log('开始清理重复的使用记录...');

    // 1. 查找所有重复的记录
    const duplicatesQuery = `
      SELECT 
        user_id,
        agent_id,
        COUNT(*) as record_count,
        SUM(usage_count) as total_usage_count
      FROM agent_usage_logs
      GROUP BY user_id, agent_id
      HAVING COUNT(*) > 1
    `;

    const duplicates = await executeQuery<any[]>(duplicatesQuery);
    console.log('找到重复记录组:', duplicates.length);

    let cleanedCount = 0;

    // 2. 对每个重复的用户+智能体组合进行清理
    for (const duplicate of duplicates) {
      const { user_id, agent_id, total_usage_count } = duplicate;
      
      console.log(`处理用户 ${user_id} 的智能体 ${agent_id}，总使用次数: ${total_usage_count}`);

      // 2.1 获取该用户+智能体的所有记录
      const recordsQuery = `
        SELECT id, usage_count, tokens_used, cost, session_id, expiry_date, created_at
        FROM agent_usage_logs
        WHERE user_id = ? AND agent_id = ?
        ORDER BY created_at DESC
      `;
      
      const records = await executeQuery<any[]>(recordsQuery, [user_id, agent_id]);
      
      if (records.length <= 1) continue;

      // 2.2 保留最新的记录，合并所有数据
      const latestRecord = records[0];
      const totalTokens = records.reduce((sum, r) => sum + (r.tokens_used || 0), 0);
      const totalCost = records.reduce((sum, r) => sum + (r.cost || 0), 0);

      // 2.3 更新最新记录，合并所有使用次数
      const updateQuery = `
        UPDATE agent_usage_logs
        SET usage_count = ?,
            tokens_used = ?,
            cost = ?
        WHERE id = ?
      `;

      await executeQuery(updateQuery, [
        total_usage_count,
        totalTokens,
        totalCost,
        latestRecord.id
      ]);

      // 2.4 删除其他旧记录
      const deleteQuery = `
        DELETE FROM agent_usage_logs
        WHERE user_id = ? AND agent_id = ? AND id != ?
      `;

      const deleteResult = await executeQuery(deleteQuery, [user_id, agent_id, latestRecord.id]);
      
      console.log(`用户 ${user_id} 智能体 ${agent_id}: 删除了 ${records.length - 1} 条重复记录`);
      cleanedCount += records.length - 1;
    }

    // 3. 验证清理结果
    const finalCheckQuery = `
      SELECT 
        user_id,
        agent_id,
        COUNT(*) as record_count
      FROM agent_usage_logs
      GROUP BY user_id, agent_id
      HAVING COUNT(*) > 1
    `;

    const remainingDuplicates = await executeQuery<any[]>(finalCheckQuery);

    return NextResponse.json({
      success: true,
      message: '使用记录清理完成',
      data: {
        duplicateGroups: duplicates.length,
        cleanedRecords: cleanedCount,
        remainingDuplicates: remainingDuplicates.length,
      },
    });

  } catch (error) {
    console.error('清理使用记录失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '清理使用记录失败',
      },
      { status: 500 }
    );
  }
}
