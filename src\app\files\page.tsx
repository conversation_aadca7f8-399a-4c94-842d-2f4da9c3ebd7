'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createStyles } from 'antd-style';
import { Card, Button, Space, Statistic, Row, Col } from 'antd';
import { FileText, Image, Download, Trash2 } from 'lucide-react';
import { useAuthStore } from '@/store/auth';
import FileUpload from '@/components/upload/FileUpload';
import { message } from '@/components/AntdStaticMethods';
import { FileInfo, fileUploadService } from '@/services/fileUpload';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: ${token.marginLG}px;
    padding: ${token.paddingLG}px;
  `,
  
  header: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: ${token.marginLG}px;
    
    h2 {
      margin: 0;
      color: ${token.colorText};
    }
  `,
  
  statsRow: css`
    margin-bottom: ${token.marginLG}px;
    
    .ant-statistic {
      text-align: center;
    }
  `,
  
  uploadSection: css`
    margin-bottom: ${token.marginLG}px;
  `,
  
  actionsBar: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: ${token.paddingSM}px 0;
    border-bottom: 1px solid ${token.colorBorder};
    margin-bottom: ${token.marginLG}px;
  `,
}));

export default function FilesPage() {
  const router = useRouter();
  const { styles } = useStyles();
  const { isLoggedIn, checkLoginStatus } = useAuthStore();
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [stats, setStats] = useState({
    totalFiles: 0,
    totalSize: 0,
    imageFiles: 0,
    textFiles: 0,
  });

  useEffect(() => {
    const initAuth = async () => {
      await checkLoginStatus();
      
      if (!isLoggedIn) {
        router.replace('/login');
      }
    };

    initAuth();
  }, [isLoggedIn, router, checkLoginStatus]);

  useEffect(() => {
    // 加载已上传的文件
    const loadFiles = () => {
      const allFiles = fileUploadService.getAllFiles();
      setFiles(allFiles);
      updateStats(allFiles);
    };

    loadFiles();
  }, []);

  // 更新统计信息
  const updateStats = (fileList: FileInfo[]) => {
    const totalSize = fileList.reduce((sum, file) => sum + file.size, 0);
    const imageFiles = fileList.filter(file => file.type.startsWith('image/')).length;
    const textFiles = fileList.filter(file => 
      file.type.startsWith('text/') || file.type === 'application/json'
    ).length;

    setStats({
      totalFiles: fileList.length,
      totalSize,
      imageFiles,
      textFiles,
    });
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理文件变化
  const handleFilesChange = (newFiles: FileInfo[]) => {
    setFiles(newFiles);
    updateStats(newFiles);
  };

  // 清空所有文件
  const handleClearAll = () => {
    fileUploadService.clearFiles();
    setFiles([]);
    updateStats([]);
    message.success('已清空所有文件');
  };

  // 导出文件列表
  const handleExportList = () => {
    const fileListData = fileUploadService.exportFileList();
    const blob = new Blob([fileListData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `file-list-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    message.success('文件列表导出成功');
  };

  if (!isLoggedIn) {
    return null;
  }

  return (
    <div className={styles.container}>
      {/* 头部 */}
      <div className={styles.header}>
        <h2>文件管理</h2>
        <Space>
          <Button 
            icon={<Download size={16} />}
            onClick={handleExportList}
            disabled={files.length === 0}
          >
            导出列表
          </Button>
          <Button 
            danger
            icon={<Trash2 size={16} />}
            onClick={handleClearAll}
            disabled={files.length === 0}
          >
            清空所有
          </Button>
        </Space>
      </div>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} className={styles.statsRow}>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="总文件数"
              value={stats.totalFiles}
              prefix={<FileText size={20} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="总大小"
              value={formatFileSize(stats.totalSize)}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="图片文件"
              value={stats.imageFiles}
              prefix={<Image size={20} />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="文本文件"
              value={stats.textFiles}
              prefix={<FileText size={20} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 文件上传区域 */}
      <Card title="文件上传" className={styles.uploadSection}>
        <FileUpload
          options={{
            maxSize: 10 * 1024 * 1024, // 10MB
            maxFiles: 20,
            allowedTypes: [
              'text/plain',
              'text/csv',
              'application/json',
              'text/markdown',
              'image/jpeg',
              'image/png',
              'image/gif',
              'image/webp',
            ],
          }}
          onFilesChange={handleFilesChange}
          showFileList={true}
          multiple={true}
        />
      </Card>
    </div>
  );
}
