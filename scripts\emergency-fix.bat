@echo off
echo 正在尝试修复MySQL连接数问题...

echo.
echo 1. 尝试以管理员身份重启MySQL服务...
net stop MySQL80
timeout /t 5
net start MySQL80

echo.
echo 2. 等待MySQL服务启动...
timeout /t 10

echo.
echo 3. 设置最大连接数...
mysql -u root -ppassword -e "SET GLOBAL max_connections = 500; SHOW VARIABLES LIKE 'max_connections';"

echo.
echo 4. 检查当前连接状态...
mysql -u root -ppassword -e "SHOW STATUS LIKE 'Threads_connected'; SHOW STATUS LIKE 'Max_used_connections';"

echo.
echo 修复完成！
pause
