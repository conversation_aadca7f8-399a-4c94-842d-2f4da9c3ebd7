import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// 健康检查API
export async function GET() {
  try {
    const healthCheck = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      checks: {
        database: 'unknown',
        memory: 'ok',
        disk: 'ok'
      }
    };

    // 检查数据库连接
    try {
      const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '3306'),
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'story_ai',
      });

      await connection.execute('SELECT 1');
      await connection.end();
      healthCheck.checks.database = 'ok';
    } catch (error) {
      console.error('Database health check failed:', error);
      healthCheck.checks.database = 'error';
      healthCheck.status = 'degraded';
    }

    // 检查内存使用情况
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };

    // 如果内存使用超过 500MB，标记为警告
    if (memoryUsageMB.heapUsed > 500) {
      healthCheck.checks.memory = 'warning';
    }

    const response = {
      ...healthCheck,
      memory: memoryUsageMB,
    };

    // 根据健康状态返回不同的HTTP状态码
    const statusCode = healthCheck.status === 'ok' ? 200 : 
                      healthCheck.status === 'degraded' ? 207 : 503;

    return NextResponse.json(response, { status: statusCode });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 503 });
  }
}
