'use client';

import { useState } from 'react';
import { Button, Card, message, Space } from 'antd';

export default function CleanupUsagePage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleCleanup = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/cleanup-usage', {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (data.success) {
        setResult(data.data);
        message.success('清理完成！');
      } else {
        message.error(data.error || '清理失败');
      }
    } catch (error) {
      console.error('清理失败:', error);
      message.error('清理失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="清理重复的使用记录">
        <Space direction="vertical" style={{ width: '100%' }}>
          <p>
            <strong>说明：</strong>
            这个工具会清理数据库中重复的使用记录，确保每个用户+智能体只有一条记录。
          </p>
          
          <Button 
            type="primary" 
            onClick={handleCleanup} 
            loading={loading}
            size="large"
          >
            开始清理
          </Button>
          
          {result && (
            <div style={{ marginTop: '16px', padding: '16px', backgroundColor: '#f6f6f6', borderRadius: '6px' }}>
              <h4>清理结果：</h4>
              <p>• 发现重复记录组：{result.duplicateGroups} 个</p>
              <p>• 清理的记录数：{result.cleanedRecords} 条</p>
              <p>• 剩余重复记录：{result.remainingDuplicates} 个</p>
              
              {result.remainingDuplicates === 0 && (
                <p style={{ color: 'green', fontWeight: 'bold' }}>✅ 所有重复记录已清理完成！</p>
              )}
            </div>
          )}
        </Space>
      </Card>
    </div>
  );
}
