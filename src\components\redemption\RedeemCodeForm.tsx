'use client';

import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Space,
  Alert,
  Divider,
  Tag,
  Row,
  Col,
  Statistic,
  App
} from 'antd';
import {
  Gift,
  CheckCircle,
  AlertCircle,
  Clock,
  Hash
} from 'lucide-react';
import { createStyles } from 'antd-style';
import { useAuthStore } from '@/store/auth';

const { Title, Text, Paragraph } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    max-width: 600px;
    margin: 0 auto;
    padding: ${token.padding}px;
  `,
  card: css`
    border-radius: ${token.borderRadius}px;
    box-shadow: ${token.boxShadowSecondary};
  `,
  codeInput: css`
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 16px;
    letter-spacing: 2px;
    text-align: center;
    text-transform: uppercase;
  `,
  successCard: css`
    background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
    border: 1px solid #b7eb8f;
  `,
  iconWrapper: css`
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: linear-gradient(135deg, #52c41a 0%, #1890ff 100%);
    color: white;
    margin: 0 auto 16px;
  `,
}));

interface RedeemResult {
  codeType: 'duration' | 'usage';
  agentName: string;
  durationDays?: number;
  usageCount?: number;
  label: string;
}

const RedeemCodeForm: React.FC = () => {
  const { styles } = useStyles();
  const { message } = App.useApp();
  const { user } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [redeemResult, setRedeemResult] = useState<RedeemResult | null>(null);

  const handleRedeem = async (values: { code: string }) => {
    if (!user?.id) {
      message.error('请先登录后再使用兑换码');
      return;
    }

    console.log('当前用户ID:', user.id); // 调试信息
    console.log('兑换码:', values.code); // 调试信息

    setLoading(true);
    try {
      const response = await fetch('/api/redemption-codes/redeem', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: values.code.toUpperCase().replace(/\s/g, ''),
          userId: user.id,
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        setRedeemResult(result.data);
        form.resetFields();
      } else {
        message.error(result.error);
      }
    } catch (error) {
      console.error('兑换码使用失败:', error);
      message.error('兑换码使用失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const formatCode = (value: string) => {
    // 自动格式化兑换码：RC-XXXX-XXXX
    const cleaned = value.replace(/[^A-Z0-9]/g, '');
    if (cleaned.length <= 2) return cleaned;
    if (cleaned.length <= 6) return `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
    return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6, 10)}`;
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCode(e.target.value.toUpperCase());
    form.setFieldsValue({ code: formatted });
  };

  // 检查用户是否已登录
  if (!user) {
    return (
      <div className={styles.container}>
        <Card className={styles.card}>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.iconWrapper}>
              <AlertCircle size={32} />
            </div>

            <Title level={3} style={{ color: '#ff4d4f', marginBottom: 8 }}>
              请先登录
            </Title>

            <Paragraph type="secondary" style={{ marginBottom: 24 }}>
              使用兑换码前需要先登录您的账户
            </Paragraph>
          </div>
        </Card>
      </div>
    );
  }

  if (redeemResult) {
    return (
      <div className={styles.container}>
        <Card className={`${styles.card} ${styles.successCard}`}>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.iconWrapper}>
              <CheckCircle size={32} />
            </div>
            
            <Title level={3} style={{ color: '#52c41a', marginBottom: 8 }}>
              兑换成功！
            </Title>
            
            <Paragraph type="secondary" style={{ marginBottom: 24 }}>
              恭喜您成功兑换了智能体使用权益
            </Paragraph>

            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Statistic
                  title="智能体"
                  value={redeemResult.agentName}
                  valueStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="兑换类型"
                  value={redeemResult.codeType === 'duration' ? '时长兑换' : '次数兑换'}
                  valueStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                />
              </Col>
            </Row>

            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Statistic
                  title="获得权益"
                  value={
                    redeemResult.codeType === 'duration' 
                      ? `${redeemResult.durationDays}天` 
                      : `${redeemResult.usageCount}次`
                  }
                  valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}
                  prefix={redeemResult.codeType === 'duration' ? <Clock /> : <Hash />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="兑换标签"
                  value={redeemResult.label}
                  valueStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                />
              </Col>
            </Row>

            <Alert
              message="权益已自动添加到您的账户"
              description="您可以立即开始使用该智能体，权益将根据您的使用情况自动扣减。"
              type="success"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Button 
              type="primary" 
              size="large"
              onClick={() => setRedeemResult(null)}
              style={{ minWidth: 120 }}
            >
              继续兑换
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Gift size={48} style={{ color: '#1890ff', marginBottom: 16 }} />
          <Title level={2} style={{ marginBottom: 8 }}>
            兑换码使用
          </Title>
          <Text type="secondary">
            输入您的兑换码来获取智能体使用权益
          </Text>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleRedeem}
          size="large"
        >
          <Form.Item
            label="兑换码"
            name="code"
            rules={[
              { required: true, message: '请输入兑换码' },
              { 
                pattern: /^RC-[A-Z0-9]{4}-[A-Z0-9]{4}$/, 
                message: '兑换码格式不正确，应为 RC-XXXX-XXXX 格式' 
              }
            ]}
          >
            <Input
              placeholder="RC-XXXX-XXXX"
              className={styles.codeInput}
              onChange={handleCodeChange}
              maxLength={12}
              autoComplete="off"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              block
              size="large"
              icon={<Gift />}
            >
              立即兑换
            </Button>
          </Form.Item>
        </Form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Title level={5} style={{ marginBottom: 16 }}>
            使用说明
          </Title>
          
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <CheckCircle size={16} style={{ color: '#52c41a', marginRight: 8 }} />
              <Text>每个兑换码只能使用一次</Text>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <CheckCircle size={16} style={{ color: '#52c41a', marginRight: 8 }} />
              <Text>兑换码格式：RC-XXXX-XXXX</Text>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <AlertCircle size={16} style={{ color: '#faad14', marginRight: 8 }} />
              <Text>请妥善保管兑换码，避免泄露</Text>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <AlertCircle size={16} style={{ color: '#faad14', marginRight: 8 }} />
              <Text>过期的兑换码无法使用</Text>
            </div>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default RedeemCodeForm;
