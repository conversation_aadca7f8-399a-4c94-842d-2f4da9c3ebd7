'use client';

import React, { useState } from 'react';
import { Button, Input, Card, Typography, Space, Alert, Divider, Select } from 'antd';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function TestGoogleAPIPage() {
  const [apiKey, setApiKey] = useState('');
  const [baseUrl, setBaseUrl] = useState('https://generativelanguage.googleapis.com');
  const [model, setModel] = useState('gemini-2.5-flash-preview-05-20');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const presetUrls = [
    { label: '官方 API', value: 'https://generativelanguage.googleapis.com' },
    { label: '代理 API (dpapi)', value: 'https://www.dpapi.top/v1' },
    { label: '自定义', value: 'custom' },
  ];

  const testConnection = async () => {
    if (!apiKey) {
      setResult({ success: false, error: '请输入 API Key' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/test-ai-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'google',
          apiKey,
          baseUrl,
          model,
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ 
        success: false, 
        error: error instanceof Error ? error.message : '测试失败' 
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Google Gemini API 连接测试</Title>
      
      <Alert
        message="URL 兼容性说明"
        description={
          <div>
            <Paragraph>
              此测试页面支持两种 API URL 格式：
            </Paragraph>
            <ul>
              <li><Text code>https://generativelanguage.googleapis.com</Text> - 官方 Google API</li>
              <li><Text code>https://www.dpapi.top/v1</Text> - 代理 API（已包含版本路径）</li>
            </ul>
            <Paragraph style={{ marginTop: 16 }}>
              代码会自动检测 URL 格式并构建正确的请求路径，避免路径重复问题。
            </Paragraph>
          </div>
        }
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Card title="测试配置">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Base URL:</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              value={baseUrl}
              onChange={(value) => {
                if (value !== 'custom') {
                  setBaseUrl(value);
                }
              }}
            >
              {presetUrls.map(url => (
                <Option key={url.value} value={url.value}>
                  {url.label}
                </Option>
              ))}
            </Select>
            {baseUrl === 'custom' && (
              <Input
                placeholder="输入自定义 Base URL"
                style={{ marginTop: 8 }}
                onChange={(e) => setBaseUrl(e.target.value)}
              />
            )}
          </div>

          <div>
            <Text strong>API Key:</Text>
            <Input.Password
              placeholder="输入你的 Google API Key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              style={{ marginTop: 8 }}
            />
          </div>
          
          <div>
            <Text strong>模型:</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              value={model}
              onChange={setModel}
            >
              <Option value="gemini-2.5-pro">Gemini 2.5 Pro</Option>
              <Option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash Preview</Option>
              <Option value="gemini-2.0-flash">Gemini 2.0 Flash</Option>
              <Option value="gemini-1.5-pro">Gemini 1.5 Pro</Option>
            </Select>
          </div>

          <Button 
            type="primary" 
            onClick={testConnection}
            loading={loading}
            style={{ marginTop: 16 }}
          >
            测试连接
          </Button>
        </Space>
      </Card>

      {result && (
        <Card title="测试结果" style={{ marginTop: 24 }}>
          {result.success ? (
            <Alert
              message="连接成功！"
              description={result.data?.message || ''}
              type="success"
              showIcon
            />
          ) : (
            <Alert
              message="连接失败"
              description={result.error}
              type="error"
              showIcon
            />
          )}
          
          <Divider />
          
          <Text strong>完整响应:</Text>
          <TextArea
            value={JSON.stringify(result, null, 2)}
            rows={10}
            readOnly
            style={{ marginTop: 8, fontFamily: 'monospace' }}
          />
        </Card>
      )}

      <Card title="URL 构建逻辑" style={{ marginTop: 24 }}>
        <Space direction="vertical">
          <div>
            <Text strong>官方 API URL:</Text>
            <Paragraph>
              <Text code>https://generativelanguage.googleapis.com</Text> →
              <Text code>https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent</Text>
            </Paragraph>
            <Text type="secondary">使用 Google 原生格式的请求体和响应</Text>
          </div>

          <div>
            <Text strong>代理 API URL:</Text>
            <Paragraph>
              <Text code>https://www.dpapi.top/v1</Text> →
              <Text code>https://www.dpapi.top/v1/chat/completions</Text>
            </Paragraph>
            <Text type="secondary">使用 OpenAI 兼容格式的请求体和响应</Text>
          </div>

          <div>
            <Text strong>自动检测逻辑:</Text>
            <ul style={{ marginTop: 8 }}>
              <li>如果 Base URL 包含 <Text code>/v1</Text> 但不包含 <Text code>/v1beta</Text>，则认为是代理 API</li>
              <li>代理 API 使用 OpenAI 兼容的 <Text code>/chat/completions</Text> 端点</li>
              <li>代理 API 使用 <Text code>Authorization: Bearer</Text> 头部认证</li>
              <li>其他情况使用标准 Google API 格式</li>
            </ul>
          </div>
        </Space>
      </Card>
    </div>
  );
}
