'use client';

import { useState, useEffect } from 'react';
import { Button, Table, Card, Space, message } from 'antd';
import { useAuthStore } from '@/store/auth';
import { useAgentStore } from '@/store/agent';

interface UsageRecord {
  id: string;
  user_id: string;
  agent_id: string;
  session_id: string;
  usage_type: string;
  usage_count: number;
  remaining_count: number;
  created_at: string;
}

interface UsageSummary {
  totalRecords: number;
  totalUsageCount: number;
  trialCount: number;
  remainingCount: number;
}

export default function DebugUsagePage() {
  const [records, setRecords] = useState<UsageRecord[]>([]);
  const [summary, setSummary] = useState<UsageSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const { user } = useAuthStore();
  const { agents } = useAgentStore();

  const fetchUsageData = async () => {
    setLoading(true);
    try {
      // 先查询所有数据
      const response = await fetch(`/api/debug-usage`);
      const result = await response.json();

      if (result.success) {
        console.log('调试数据:', result.data);
        // 显示所有用户和智能体的信息
        message.info(`找到 ${result.data.users?.length || 0} 个用户，${result.data.agents?.length || 0} 个智能体`);

        // 如果有使用记录，显示第一个
        if (result.data.usageByUserAgent && result.data.usageByUserAgent.length > 0) {
          const firstUsage = result.data.usageByUserAgent[0];
          // 查询具体的使用记录
          const detailResponse = await fetch(`/api/debug-usage?userId=${firstUsage.user_id}&agentId=${firstUsage.agent_id}`);
          const detailResult = await detailResponse.json();

          if (detailResult.success) {
            setRecords(detailResult.data.records);
            setSummary(detailResult.data.summary);
          }
        }
      } else {
        message.error(result.error || '查询失败');
      }
    } catch (error) {
      console.error('查询使用记录失败:', error);
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsageData();
  }, []);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      ellipsis: true,
    },
    {
      title: '会话ID',
      dataIndex: 'session_id',
      key: 'session_id',
      width: 200,
      ellipsis: true,
    },
    {
      title: '使用类型',
      dataIndex: 'usage_type',
      key: 'usage_type',
      width: 100,
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 100,
    },
    {
      title: '剩余次数',
      dataIndex: 'remaining_count',
      key: 'remaining_count',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
  ];



  return (
    <div style={{ padding: '24px' }}>
      <Card title="使用记录调试" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button type="primary" onClick={fetchUsageData} loading={loading}>
            刷新数据
          </Button>
          
          {summary && (
            <div>
              <p><strong>总记录数:</strong> {summary.totalRecords}</p>
              <p><strong>总使用次数:</strong> {summary.totalUsageCount}</p>
              <p><strong>试用次数:</strong> {summary.trialCount}</p>
              <p><strong>剩余次数:</strong> {summary.remainingCount}</p>
            </div>
          )}
        </Space>
      </Card>

      <Card title="使用记录详情">
        <Table
          columns={columns}
          dataSource={records}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
}
