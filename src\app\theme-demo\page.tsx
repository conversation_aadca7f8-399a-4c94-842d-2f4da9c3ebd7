'use client';

import { Card, Button, Input, Typography, Space, Row, Col, Tag, Alert } from 'antd';
import { useTheme } from 'antd-style';
import ThemeSwitch from '@/components/ThemeSwitch';
import ColorPicker from '@/components/ColorPicker';

const { Title, Text, Paragraph } = Typography;

export default function ThemeDemo() {
  const theme = useTheme();

  return (
    <div style={{ 
      padding: '24px',
      minHeight: '100vh',
      backgroundColor: theme.colorBgLayout,
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 页面标题和主题切换 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '32px'
        }}>
          <Title level={1} style={{ margin: 0 }}>
            Lobe Chat 颜色体系演示
          </Title>
          <ThemeSwitch />
        </div>

        <Row gutter={[24, 24]}>
          {/* 颜色配置 */}
          <Col xs={24} lg={8}>
            <ColorPicker />
          </Col>

          {/* 组件演示 */}
          <Col xs={24} lg={16}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 按钮演示 */}
              <Card title="按钮组件">
                <Space wrap>
                  <Button type="primary">主要按钮</Button>
                  <Button>默认按钮</Button>
                  <Button type="dashed">虚线按钮</Button>
                  <Button type="text">文本按钮</Button>
                  <Button type="link">链接按钮</Button>
                  <Button danger>危险按钮</Button>
                </Space>
              </Card>

              {/* 输入框演示 */}
              <Card title="输入组件">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Input placeholder="基础输入框" />
                  <Input.Search placeholder="搜索输入框" />
                  <Input.TextArea placeholder="文本域" rows={3} />
                </Space>
              </Card>

              {/* 标签和状态 */}
              <Card title="标签和状态">
                <Space wrap>
                  <Tag color="blue">蓝色标签</Tag>
                  <Tag color="green">绿色标签</Tag>
                  <Tag color="orange">橙色标签</Tag>
                  <Tag color="red">红色标签</Tag>
                  <Tag color="purple">紫色标签</Tag>
                </Space>
              </Card>

              {/* 提示信息 */}
              <Card title="提示信息">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Alert message="信息提示" type="info" showIcon />
                  <Alert message="成功提示" type="success" showIcon />
                  <Alert message="警告提示" type="warning" showIcon />
                  <Alert message="错误提示" type="error" showIcon />
                </Space>
              </Card>

              {/* 文字演示 */}
              <Card title="文字层级">
                <Space direction="vertical">
                  <Title level={2}>二级标题</Title>
                  <Title level={3}>三级标题</Title>
                  <Title level={4}>四级标题</Title>
                  <Paragraph>
                    这是一段正文内容，展示了 Lobe Chat 颜色体系中的文字颜色。
                    采用 Google Material Design 的颜色规范，确保良好的可读性和视觉层次。
                  </Paragraph>
                  <Text type="secondary">次要文字</Text>
                  <br />
                  <Text type="success">成功文字</Text>
                  <br />
                  <Text type="warning">警告文字</Text>
                  <br />
                  <Text type="danger">危险文字</Text>
                </Space>
              </Card>

              {/* 颜色说明 */}
              <Card title="颜色体系说明">
                <Paragraph>
                  本项目已成功应用 Lobe Chat 的颜色体系，主要特点：
                </Paragraph>
                <ul>
                  <li><strong>主色调</strong>：采用 Google 蓝色 (#1a73e8) 作为品牌主色</li>
                  <li><strong>功能色</strong>：成功(绿)、警告(黄)、错误(红)、信息(蓝)</li>
                  <li><strong>中性色</strong>：基于 Google Material Design 的灰色系</li>
                  <li><strong>深色模式</strong>：支持自动切换和手动切换</li>
                  <li><strong>自定义主题</strong>：支持主色调和中性色的自定义</li>
                </ul>
              </Card>
            </Space>
          </Col>
        </Row>
      </div>
    </div>
  );
}
