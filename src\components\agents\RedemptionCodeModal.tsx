'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  Table,
  Tag,
  Tabs,
  DatePicker,
  Popconfirm,
  Typography,
  Card,
  Statistic,
  Row,
  Col,
  Radio,
  App
} from 'antd';
import {
  Gift,
  Download,
  Eye,
  Trash2,
  Calendar,
  Hash,
  Clock,
  Users,
  ArrowLeft
} from 'lucide-react';
import dayjs from 'dayjs';
import * as XLSX from 'xlsx';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface RedemptionCodeModalProps {
  visible: boolean;
  onCancel: () => void;
  agentId: string;
  agentName?: string;
}

interface RedemptionBatch {
  id: string;
  agentId: string;
  batchName: string;
  codeType: 'duration' | 'usage';
  label: string;
  totalCount: number;
  usedCount: number;
  durationDays?: number;
  usageCount?: number;
  expiresAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface RedemptionCode {
  id: string;
  agentId: string;
  code: string;
  codeType: 'duration' | 'usage';
  label: string;
  durationDays?: number;
  usageCount?: number;
  usedAt?: string;
  usedBy?: string;
  expiresAt?: string;
  createdAt: string;
  batchId?: string;
  batchName?: string;
}

const RedemptionCodeModal: React.FC<RedemptionCodeModalProps> = ({
  visible,
  onCancel,
  agentId,
  agentName
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('generate');
  const [codeType, setCodeType] = useState<'duration' | 'usage'>('duration');
  const [batches, setBatches] = useState<RedemptionBatch[]>([]);
  const [codes, setCodes] = useState<RedemptionCode[]>([]);
  const [selectedBatch, setSelectedBatch] = useState<string>('');
  const [generatedBatch, setGeneratedBatch] = useState<any>(null);

  // 加载兑换码批次
  const loadBatches = async () => {
    try {
      const response = await fetch(`/api/redemption-codes?agentId=${agentId}&type=batches`);
      const result = await response.json();
      if (result.success) {
        setBatches(result.data || []);
      }
    } catch (error) {
      console.error('加载兑换码批次失败:', error);
    }
  };

  // 加载兑换码列表
  const loadCodes = async (batchId?: string) => {
    try {
      const url = batchId 
        ? `/api/redemption-codes?agentId=${agentId}&batchId=${batchId}`
        : `/api/redemption-codes?agentId=${agentId}`;
      const response = await fetch(url);
      const result = await response.json();
      if (result.success) {
        setCodes(result.data || []);
      }
    } catch (error) {
      console.error('加载兑换码列表失败:', error);
    }
  };

  // 生成兑换码
  const handleGenerate = async (values: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/redemption-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId,
          codeType: values.codeType,
          label: values.label,
          count: values.count,
          durationDays: values.codeType === 'duration' ? values.durationDays : undefined,
          usageCount: values.codeType === 'usage' ? values.usageCount : undefined,
          expiresAt: values.expiresAt ? values.expiresAt.toISOString() : undefined,
          createdBy: 'mdoi3uqorptmrc7zwr', // 使用实际存在的用户ID
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        setGeneratedBatch(result.data);
        form.resetFields();
        loadBatches();
        setActiveTab('download');
      } else {
        message.error(result.error);
      }
    } catch (error) {
      console.error('生成兑换码失败:', error);
      message.error('生成兑换码失败');
    } finally {
      setLoading(false);
    }
  };

  // 下载兑换码Excel
  const handleDownload = async (batchId: string) => {
    try {
      const response = await fetch(`/api/redemption-codes/export?batchId=${batchId}`);

      if (!response.ok) {
        throw new Error('下载失败');
      }

      // 解析JSON响应
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '下载失败');
      }

      // 使用xlsx库在前端生成Excel文件
      const { codes, instructions, stats, filename } = result.data;

      // 创建工作簿
      const workbook = XLSX.utils.book_new();

      // 创建兑换码列表工作表
      const codesWorksheet = XLSX.utils.aoa_to_sheet(codes);
      codesWorksheet['!cols'] = [
        { width: 20 }, // 兑换码
        { width: 12 }, // 类型
        { width: 15 }, // 标签
        { width: 10 }, // 价值
        { width: 12 }, // 有效期
        { width: 10 }, // 状态
        { width: 15 }, // 使用者
        { width: 20 }, // 使用时间
        { width: 20 }, // 创建时间
      ];
      XLSX.utils.book_append_sheet(workbook, codesWorksheet, '兑换码列表');

      // 创建使用说明工作表
      const instructionsWorksheet = XLSX.utils.aoa_to_sheet(instructions);
      instructionsWorksheet['!cols'] = [{ width: 60 }];
      XLSX.utils.book_append_sheet(workbook, instructionsWorksheet, '使用说明');

      // 创建统计信息工作表
      const statsWorksheet = XLSX.utils.aoa_to_sheet(stats);
      statsWorksheet['!cols'] = [{ width: 20 }, { width: 20 }];
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, '统计信息');

      // 生成Excel文件并下载
      const excelBuffer = XLSX.write(workbook, {
        type: 'array',
        bookType: 'xlsx',
        compression: true
      });

      // 创建blob并下载
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `兑换码_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('兑换码Excel下载成功');
    } catch (error) {
      console.error('下载兑换码Excel失败:', error);
      message.error(`下载兑换码Excel失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除兑换码批次
  const handleDeleteBatch = async (batchId: string) => {
    try {
      const response = await fetch(`/api/redemption-codes?batchId=${batchId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        loadBatches();
        if (selectedBatch === batchId) {
          setSelectedBatch('');
          setCodes([]);
        }
      } else {
        message.error(result.error);
      }
    } catch (error) {
      console.error('删除兑换码批次失败:', error);
      message.error('删除兑换码批次失败');
    }
  };

  // 查看兑换码详情
  const handleViewCodes = (batchId: string) => {
    setSelectedBatch(batchId);
    loadCodes(batchId);
    setActiveTab('history');
  };

  useEffect(() => {
    if (visible) {
      loadBatches();
      setActiveTab('generate');
      setGeneratedBatch(null);
      form.resetFields();
    }
  }, [visible]);

  // 批次表格列定义
  const batchColumns = [
    {
      title: '批次名称',
      dataIndex: 'batchName',
      key: 'batchName',
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'codeType',
      key: 'codeType',
      width: 100,
      render: (type: string) => (
        <Tag color={type === 'duration' ? 'blue' : 'green'}>
          {type === 'duration' ? '时长兑换' : '次数兑换'}
        </Tag>
      ),
    },
    {
      title: '价值',
      key: 'value',
      width: 100,
      render: (record: RedemptionBatch) => (
        <Text>
          {record.codeType === 'duration' 
            ? `${record.durationDays}天` 
            : `${record.usageCount}次`}
        </Text>
      ),
    },
    {
      title: '总数/已用',
      key: 'usage',
      width: 120,
      render: (record: RedemptionBatch) => (
        <Text>
          {record.usedCount}/{record.totalCount}
        </Text>
      ),
    },
    {
      title: '使用率',
      key: 'rate',
      width: 100,
      render: (record: RedemptionBatch) => {
        const rate = (record.usedCount / record.totalCount * 100).toFixed(1);
        return <Text>{rate}%</Text>;
      },
    },
    {
      title: '有效期',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      width: 120,
      render: (date: string) => (
        <Text>{date ? dayjs(date).format('YYYY-MM-DD') : '永久有效'}</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => (
        <Text>{dayjs(date).format('YYYY-MM-DD HH:mm')}</Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (record: RedemptionBatch) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<Download size={14} />}
            onClick={() => handleDownload(record.id)}
          >
            下载
          </Button>
          <Button
            type="text"
            size="small"
            icon={<Eye size={14} />}
            onClick={() => handleViewCodes(record.id)}
          >
            查看
          </Button>
          <Popconfirm
            title="确定要删除这个兑换码批次吗？"
            description="删除后无法恢复，且只能删除未使用的批次"
            onConfirm={() => handleDeleteBatch(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<Trash2 size={14} />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 兑换码表格列定义
  const codeColumns = [
    {
      title: '兑换码',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (code: string) => (
        <Text code copyable>{code}</Text>
      ),
    },
    {
      title: '类型',
      dataIndex: 'codeType',
      key: 'codeType',
      width: 100,
      render: (type: string) => (
        <Tag color={type === 'duration' ? 'blue' : 'green'}>
          {type === 'duration' ? '时长兑换' : '次数兑换'}
        </Tag>
      ),
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      width: 120,
    },
    {
      title: '价值',
      key: 'value',
      width: 100,
      render: (record: RedemptionCode) => (
        <Text>
          {record.codeType === 'duration'
            ? `${record.durationDays}天`
            : `${record.usageCount}次`}
        </Text>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: RedemptionCode) => (
        <Tag color={record.usedAt ? 'red' : 'green'}>
          {record.usedAt ? '已使用' : '未使用'}
        </Tag>
      ),
    },
    {
      title: '使用者',
      dataIndex: 'usedBy',
      key: 'usedBy',
      width: 120,
      render: (usedBy: string) => usedBy || '-',
    },
    {
      title: '使用时间',
      dataIndex: 'usedAt',
      key: 'usedAt',
      width: 150,
      render: (date: string) => (
        <Text>{date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-'}</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => (
        <Text>{dayjs(date).format('YYYY-MM-DD HH:mm')}</Text>
      ),
    },
  ];

  return (
    <Modal
      title={`兑换码管理 - ${agentName || '智能体'}`}
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      destroyOnClose
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="生成兑换码" key="generate">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleGenerate}
            initialValues={{
              codeType: 'duration',
              count: 10,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="兑换码类型"
                  name="codeType"
                  rules={[{ required: true, message: '请选择兑换码类型' }]}
                >
                  <Radio.Group onChange={(e) => setCodeType(e.target.value)}>
                    <Radio value="duration">时长兑换</Radio>
                    <Radio value="usage">次数兑换</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="兑换码标签"
                  name="label"
                  rules={[{ required: true, message: '请输入兑换码标签' }]}
                >
                  <Input placeholder="例如：新用户福利、活动奖励等" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {codeType === 'duration' && (
                <Col span={12}>
                  <Form.Item
                    label="兑换天数"
                    name="durationDays"
                    rules={[{ required: true, message: '请输入兑换天数' }]}
                  >
                    <InputNumber
                      min={1}
                      max={365}
                      placeholder="请输入天数"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              )}
              
              {codeType === 'usage' && (
                <Col span={12}>
                  <Form.Item
                    label="兑换次数"
                    name="usageCount"
                    rules={[{ required: true, message: '请输入兑换次数' }]}
                  >
                    <InputNumber
                      min={1}
                      max={10000}
                      placeholder="请输入次数"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label="兑换码个数"
                  name="count"
                  rules={[{ required: true, message: '请输入兑换码个数' }]}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                    placeholder="请输入个数"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="有效期"
                  name="expiresAt"
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="不设置则永久有效"
                    disabledDate={(current) => current && current < dayjs().endOf('day')}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<Gift />}>
                生成兑换码
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        <TabPane tab="下载兑换码" key="download">
          {generatedBatch ? (
            <Card>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="生成数量"
                    value={generatedBatch.totalCount}
                    suffix="个"
                    prefix={<Gift />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="批次名称"
                    value={generatedBatch.batchName}
                    valueStyle={{ fontSize: '16px' }}
                  />
                </Col>
                <Col span={12}>
                  <Button
                    type="primary"
                    size="large"
                    icon={<Download />}
                    onClick={() => handleDownload(generatedBatch.batchId)}
                    style={{ marginTop: '16px' }}
                  >
                    下载兑换码Excel
                  </Button>
                </Col>
              </Row>
            </Card>
          ) : (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Text type="secondary">请先生成兑换码</Text>
            </div>
          )}
        </TabPane>

        <TabPane tab="历史兑换码" key="history">
          {selectedBatch ? (
            <div>
              <div style={{ marginBottom: 16 }}>
                <Button
                  icon={<ArrowLeft size={14} />}
                  onClick={() => {
                    setSelectedBatch('');
                    setCodes([]);
                  }}
                >
                  返回批次列表
                </Button>
              </div>
              <Table
                columns={codeColumns}
                dataSource={codes}
                rowKey="id"
                scroll={{ x: 1200 }}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 个兑换码`,
                }}
              />
            </div>
          ) : (
            <Table
              columns={batchColumns}
              dataSource={batches}
              rowKey="id"
              scroll={{ x: 1000 }}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          )}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default RedemptionCodeModal;
