'use client';

import React, { useState } from 'react';
import { createStyles } from 'antd-style';
import { useRouter, usePathname } from 'next/navigation';
import { <PERSON><PERSON>, Tooltip, Avatar, Dropdown } from 'antd';
import { 
  MessageSquare, 
  Zap, 
  Settings, 
  User, 
  LogOut,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Bot,
  FileText,
  Wrench
} from 'lucide-react';
import { useAuthStore } from '@/store/auth';

const useStyles = createStyles(({ token, css }) => ({
  sidebar: css`
    width: 280px;
    height: 100vh;
    background: ${token.colorBgContainer};
    border-right: 1px solid ${token.colorBorderSecondary};
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 100;
    
    &.collapsed {
      width: 64px;
    }
  `,
  
  header: css`
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 18px;
      font-weight: 600;
      color: ${token.colorText};
      
      .icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, ${token.colorPrimary}, ${token.colorPrimaryActive});
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        box-shadow: 0 2px 8px ${token.colorPrimary}40;
      }
      
      .text {
        transition: opacity 0.3s;
      }
    }
    
    .collapsed & {
      justify-content: center;
      
      .logo .text {
        opacity: 0;
        width: 0;
        overflow: hidden;
      }
    }
  `,
  
  nav: css`
    flex: 1;
    padding: 16px 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  `,
  
  navItem: css`
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    color: ${token.colorTextSecondary};
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    
    &:hover {
      background: ${token.colorFillTertiary};
      color: ${token.colorText};
    }
    
    &.active {
      background: ${token.colorPrimary}15;
      color: ${token.colorPrimary};
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 20px;
        background: ${token.colorPrimary};
        border-radius: 0 2px 2px 0;
      }
    }
    
    .icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }
    
    .text {
      font-size: 14px;
      font-weight: 500;
      transition: opacity 0.3s;
    }
    
    .collapsed & {
      justify-content: center;
      padding: 12px;
      
      .text {
        opacity: 0;
        width: 0;
        overflow: hidden;
      }
    }
  `,
  
  footer: css`
    padding: 16px 8px;
    border-top: 1px solid ${token.colorBorderSecondary};
    display: flex;
    flex-direction: column;
    gap: 8px;
  `,
  
  userSection: css`
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background: ${token.colorFillTertiary};
    }
    
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: ${token.colorPrimary};
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
      flex-shrink: 0;
    }
    
    .info {
      flex: 1;
      min-width: 0;
      transition: opacity 0.3s;
      
      .name {
        font-size: 14px;
        font-weight: 500;
        color: ${token.colorText};
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .status {
        font-size: 12px;
        color: ${token.colorTextTertiary};
      }
    }
    
    .collapsed & {
      justify-content: center;
      padding: 12px;
      
      .info {
        opacity: 0;
        width: 0;
        overflow: hidden;
      }
    }
  `,
  
  collapseButton: css`
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    z-index: 10;
    
    &:hover {
      background: ${token.colorFillTertiary};
      border-color: ${token.colorPrimary};
    }
    
    .icon {
      width: 12px;
      height: 12px;
      color: ${token.colorTextSecondary};
    }
  `,
  
  section: css`
    margin-bottom: 16px;
    
    .title {
      font-size: 12px;
      color: ${token.colorTextTertiary};
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 0 16px 8px;
      transition: opacity 0.3s;
    }
    
    .collapsed & .title {
      opacity: 0;
      height: 0;
      padding: 0;
      margin: 0;
      overflow: hidden;
    }
  `,
}));

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const { styles } = useStyles();
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuthStore();
  const [collapsed, setCollapsed] = useState(false);

  const navigationItems = [
    {
      key: 'chat',
      icon: <MessageSquare className="icon" />,
      text: '智能会话',
      path: '/chat',
    },
    {
      key: 'batch',
      icon: <Zap className="icon" />,
      text: '批量生成',
      path: '/batch',
    },
    {
      key: 'settings',
      icon: <Settings className="icon" />,
      text: '配置页面',
      path: '/settings',
    },
  ];

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const handleLogout = () => {
    logout();
    router.replace('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <User size={16} />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <Settings size={16} />,
      label: '账户设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogOut size={16} />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <div className={`${styles.sidebar} ${collapsed ? 'collapsed' : ''} ${className || ''}`}>
      {/* 头部 */}
      <div className={styles.header}>
        <div className="logo">
          <div className="icon">
            <Sparkles />
          </div>
          <span className="text">Gemini AI</span>
        </div>
      </div>

      {/* 导航区域 */}
      <div className={styles.nav}>
        <div className={styles.section}>
          <div className="title">功能</div>
          {navigationItems.map((item) => {
            const isActive = pathname === item.path;
            return (
              <Tooltip
                key={item.key}
                title={collapsed ? item.text : ''}
                placement="right"
              >
                <div
                  className={`${styles.navItem} ${isActive ? 'active' : ''}`}
                  onClick={() => handleNavigation(item.path)}
                >
                  {item.icon}
                  <span className="text">{item.text}</span>
                </div>
              </Tooltip>
            );
          })}
        </div>
      </div>

      {/* 底部用户区域 */}
      <div className={styles.footer}>
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="topRight"
          trigger={['click']}
        >
          <div className={styles.userSection}>
            <div className="avatar">
              {user?.username?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div className="info">
              <div className="name">{user?.username || '用户'}</div>
              <div className="status">在线</div>
            </div>
          </div>
        </Dropdown>
      </div>

      {/* 折叠按钮 */}
      <div
        className={styles.collapseButton}
        onClick={() => setCollapsed(!collapsed)}
      >
        {collapsed ? (
          <ChevronRight className="icon" />
        ) : (
          <ChevronLeft className="icon" />
        )}
      </div>
    </div>
  );
};

export default Sidebar;
