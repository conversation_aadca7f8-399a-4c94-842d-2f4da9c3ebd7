import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';
import { verifyCode, isCodeExpired, validateCodeFormat } from '@/lib/redemption-code';

// POST - 使用兑换码
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { code, userId } = body;

    console.log('兑换请求 - 兑换码:', code, '用户ID:', userId); // 调试信息

    if (!code || !userId) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码和用户ID为必填项',
        },
        { status: 400 }
      );
    }

    // 验证用户是否存在
    const userCheck = await executeQuery<any[]>('SELECT id FROM users WHERE id = ?', [userId]);
    if (userCheck.length === 0) {
      console.log('用户不存在:', userId); // 调试信息
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在，请重新登录',
        },
        { status: 400 }
      );
    }

    // 验证兑换码格式
    if (!validateCodeFormat(code)) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码格式不正确',
        },
        { status: 400 }
      );
    }

    // 查询兑换码信息
    const codeQuery = `
      SELECT rc.*, a.name as agent_name
      FROM redemption_codes rc
      LEFT JOIN agents a ON rc.agent_id = a.id
      WHERE rc.code = ?
    `;
    
    const codeResults = await executeQuery<any[]>(codeQuery, [code]);
    
    if (codeResults.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码不存在',
        },
        { status: 404 }
      );
    }

    const redemptionCode = codeResults[0];

    // 验证兑换码哈希
    if (!verifyCode(code, redemptionCode.hash_code, redemptionCode.salt)) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码验证失败',
        },
        { status: 400 }
      );
    }

    // 检查是否已使用
    if (redemptionCode.used_at) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码已被使用',
        },
        { status: 400 }
      );
    }

    // 检查是否过期
    if (isCodeExpired(redemptionCode.expires_at)) {
      return NextResponse.json(
        {
          success: false,
          error: '兑换码已过期',
        },
        { status: 400 }
      );
    }

    // 检查用户是否已经使用过同一批次的兑换码
    if (redemptionCode.batch_id) {
      const duplicateQuery = `
        SELECT COUNT(*) as count
        FROM redemption_logs rl
        JOIN redemption_codes rc ON rl.redemption_code_id = rc.id
        WHERE rl.user_id = ? AND rc.batch_id = ?
      `;
      
      const duplicateResult = await executeQuery<any[]>(duplicateQuery, [userId, redemptionCode.batch_id]);
      
      if (duplicateResult[0]?.count > 0) {
        return NextResponse.json(
          {
            success: false,
            error: '您已经使用过该批次的兑换码',
          },
          { status: 400 }
        );
      }
    }

    // 获取客户端信息
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const now = new Date();

    // 开始事务处理
    try {
      // 标记兑换码为已使用
      const updateCodeQuery = `
        UPDATE redemption_codes
        SET used_at = ?, used_by = ?, updated_at = ?
        WHERE id = ?
      `;
      
      await executeQuery(updateCodeQuery, [now, userId, now, redemptionCode.id]);

      // 创建兑换记录
      const logId = generateId();
      const logQuery = `
        INSERT INTO redemption_logs (
          id, redemption_code_id, user_id, agent_id, code_type,
          duration_days, usage_count, ip_address, user_agent, redeemed_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      await executeQuery(logQuery, [
        logId,
        redemptionCode.id,
        userId,
        redemptionCode.agent_id,
        redemptionCode.code_type,
        redemptionCode.duration_days || null,
        redemptionCode.usage_count || null,
        clientIp,
        userAgent,
        now,
      ]);

      // 更新批次使用统计
      if (redemptionCode.batch_id) {
        const updateBatchQuery = `
          UPDATE redemption_batches 
          SET used_count = used_count + 1, updated_at = ?
          WHERE id = ?
        `;
        
        await executeQuery(updateBatchQuery, [now, redemptionCode.batch_id]);
      }

      // 应用兑换码效果到用户账户
      if (redemptionCode.code_type === 'duration') {
        // 时长兑换：为用户添加智能体使用时长
        await applyDurationRedemption(userId, redemptionCode.agent_id, redemptionCode.duration_days);
      } else if (redemptionCode.code_type === 'usage') {
        // 次数兑换：为用户添加智能体使用次数
        await applyUsageRedemption(userId, redemptionCode.agent_id, redemptionCode.usage_count);
      }

      return NextResponse.json({
        success: true,
        data: {
          codeType: redemptionCode.code_type,
          agentName: redemptionCode.agent_name,
          durationDays: redemptionCode.duration_days,
          usageCount: redemptionCode.usage_count,
          label: redemptionCode.label,
        },
        message: '兑换码使用成功',
      });

    } catch (transactionError) {
      console.error('兑换码使用事务失败:', transactionError);
      return NextResponse.json(
        {
          success: false,
          error: '兑换码使用失败，请稍后重试',
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('使用兑换码失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '使用兑换码失败',
      },
      { status: 500 }
    );
  }
}

// 应用时长兑换
async function applyDurationRedemption(userId: string, agentId: string, durationDays: number) {
  // 查找或创建用户智能体订阅记录
  const subscriptionQuery = `
    SELECT * FROM user_agent_subscriptions 
    WHERE user_id = ? AND agent_id = ? AND status = 'active'
    ORDER BY end_date DESC LIMIT 1
  `;
  
  const subscriptions = await executeQuery<any[]>(subscriptionQuery, [userId, agentId]);
  
  const now = new Date();
  const extensionDays = durationDays;
  
  if (subscriptions.length > 0) {
    // 延长现有订阅
    const subscription = subscriptions[0];
    const currentEndDate = new Date(subscription.end_date);
    const newEndDate = new Date(currentEndDate.getTime() + extensionDays * 24 * 60 * 60 * 1000);
    
    const updateQuery = `
      UPDATE user_agent_subscriptions 
      SET end_date = ?, updated_at = ?
      WHERE id = ?
    `;
    
    await executeQuery(updateQuery, [newEndDate, now, subscription.id]);
  } else {
    // 创建新订阅
    const subscriptionId = generateId();
    const endDate = new Date(now.getTime() + extensionDays * 24 * 60 * 60 * 1000);
    
    const insertQuery = `
      INSERT INTO user_agent_subscriptions (
        id, user_id, agent_id, pricing_plan_id, start_date, end_date,
        status, purchase_price, currency, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await executeQuery(insertQuery, [
      subscriptionId,
      userId,
      agentId,
      'redemption_duration', // 特殊标识
      now,
      endDate,
      'active',
      0, // 兑换码获得的免费
      'CNY',
      now,
      now,
    ]);
  }
}

// 应用次数兑换
async function applyUsageRedemption(userId: string, agentId: string, usageCount: number) {
  // 查找或创建用户智能体订阅记录
  const subscriptionQuery = `
    SELECT * FROM user_agent_subscriptions 
    WHERE user_id = ? AND agent_id = ? AND status = 'active'
    ORDER BY remaining_usage DESC LIMIT 1
  `;
  
  const subscriptions = await executeQuery<any[]>(subscriptionQuery, [userId, agentId]);
  
  const now = new Date();
  
  if (subscriptions.length > 0) {
    // 增加现有订阅的使用次数
    const subscription = subscriptions[0];
    
    const updateQuery = `
      UPDATE user_agent_subscriptions 
      SET remaining_usage = remaining_usage + ?, updated_at = ?
      WHERE id = ?
    `;
    
    await executeQuery(updateQuery, [usageCount, now, subscription.id]);
  } else {
    // 创建新订阅
    const subscriptionId = generateId();
    
    const insertQuery = `
      INSERT INTO user_agent_subscriptions (
        id, user_id, agent_id, pricing_plan_id, remaining_usage, total_usage,
        status, purchase_price, currency, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await executeQuery(insertQuery, [
      subscriptionId,
      userId,
      agentId,
      'redemption_usage', // 特殊标识
      usageCount,
      usageCount,
      'active',
      0, // 兑换码获得的免费
      'CNY',
      now,
      now,
    ]);
  }
}
