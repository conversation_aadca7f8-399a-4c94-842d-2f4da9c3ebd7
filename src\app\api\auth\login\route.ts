import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// POST - 用户登录
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, rememberMe } = body;

    // 基础验证
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名/邮箱和密码都是必填的',
        },
        { status: 400 }
      );
    }

    // 查找用户 - 支持邮箱或用户名登录
    const users = await executeQuery(
      'SELECT id, username, email, password_hash, is_verified, avatar, provider FROM users WHERE email = ? OR username = ?',
      [email, email]
    );

    if (users.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名/邮箱或密码错误',
        },
        { status: 401 }
      );
    }

    const user = users[0];

    // 检查是否为第三方登录用户
    if (user.provider !== 'local') {
      return NextResponse.json(
        {
          success: false,
          error: `该账号已通过${user.provider}注册，请使用对应方式登录`,
        },
        { status: 401 }
      );
    }

    // 验证密码
    console.log(`Comparing password: ${password} with hash: ${user.password_hash}`);
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    console.log(`Password valid: ${isPasswordValid}`);
    if (!isPasswordValid) {
      console.log(`Login failed for user ${email}: Invalid password`);
      return NextResponse.json(
        {
          success: false,
          error: '用户名/邮箱或密码错误',
        },
        { status: 401 }
      );
    }

    // 检查邮箱是否已验证
    console.log(`User ${email} verification status: ${user.is_verified}`);
    if (!user.is_verified) {
      console.log(`Login failed for user ${email}: Email not verified`);
      return NextResponse.json(
        {
          success: false,
          error: '请先验证邮箱后再登录',
          needsVerification: true,
        },
        { status: 401 }
      );
    }

    // 生成JWT token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
    };

    const expiresIn = rememberMe ? '7d' : '1h';
    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn });

    // 更新登录信息
    await executeQuery(
      'UPDATE users SET last_login_at = ?, login_count = login_count + 1 WHERE id = ?',
      [new Date(), user.id]
    );

    // 返回用户信息和token
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      provider: user.provider,
    };

    return NextResponse.json({
      success: true,
      message: '登录成功',
      data: {
        user: userData,
        token,
        expiresIn,
      },
    });
  } catch (error) {
    console.error('用户登录失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '登录失败，请重试',
      },
      { status: 500 }
    );
  }
}
