-- 更新智能体表，添加缺失的字段
ALTER TABLE agents 
ADD COLUMN IF NOT EXISTS enabled BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS coze_api_key TEXT,
ADD COLUMN IF NOT EXISTS coze_bot_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS coze_user_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS system_prompt TEXT,
ADD COLUMN IF NOT EXISTS model VARCHAR(100) DEFAULT 'gpt-3.5-turbo',
ADD COLUMN IF NOT EXISTS temperature DECIMAL(3,2) DEFAULT 0.7,
ADD COLUMN IF NOT EXISTS max_tokens INT DEFAULT 2000,
ADD COLUMN IF NOT EXISTS is_favorite BOOLEAN DEFAULT FALSE;

-- 创建智能体定价模式表
CREATE TABLE IF NOT EXISTS agent_pricing_plans (
    id VARCHAR(50) PRIMARY KEY,
    agent_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type ENUM('per_usage', 'time_based') NOT NULL,
    -- 按次收费字段
    usage_count INT NULL, -- 可用次数
    price_per_usage DECIMAL(10,2) NULL, -- 每次价格
    -- 按时计费字段
    duration_days INT NULL, -- 时长（天）
    price_per_period DECIMAL(10,2) NULL, -- 每期价格
    -- 通用字段
    currency VARCHAR(10) DEFAULT 'CNY',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_agent_id (agent_id),
    INDEX idx_type (type),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户智能体订阅表（记录用户购买的智能体服务）
CREATE TABLE IF NOT EXISTS user_agent_subscriptions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    agent_id VARCHAR(50) NOT NULL,
    pricing_plan_id VARCHAR(50) NOT NULL,
    -- 按次收费相关
    remaining_usage INT DEFAULT 0, -- 剩余使用次数
    total_usage INT DEFAULT 0, -- 总使用次数
    -- 按时计费相关
    start_date TIMESTAMP NULL, -- 开始时间
    end_date TIMESTAMP NULL, -- 结束时间
    -- 通用字段
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    purchase_price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_pricing_plan_id (pricing_plan_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date),
    UNIQUE KEY unique_user_agent_plan (user_id, agent_id, pricing_plan_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (pricing_plan_id) REFERENCES agent_pricing_plans(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建智能体使用记录表（用于统计和计费）
CREATE TABLE IF NOT EXISTS agent_usage_logs (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    agent_id VARCHAR(50) NOT NULL,
    subscription_id VARCHAR(50),
    session_id VARCHAR(50),
    usage_type ENUM('chat', 'generation') DEFAULT 'chat',
    tokens_used INT DEFAULT 0,
    cost DECIMAL(10,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_agent_subscriptions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例定价方案
INSERT IGNORE INTO agent_pricing_plans (id, agent_id, name, type, usage_count, price_per_usage, currency, description, is_active) VALUES
('pricing_1', 'agent_1', '基础套餐', 'per_usage', 100, 0.10, 'CNY', '100次对话，适合轻度使用', true),
('pricing_2', 'agent_1', '专业套餐', 'per_usage', 500, 0.08, 'CNY', '500次对话，更优惠的单价', true),
('pricing_3', 'agent_1', '月度套餐', 'time_based', NULL, NULL, 'CNY', '30天无限使用', true);

-- 更新月度套餐的时间计费字段
UPDATE agent_pricing_plans 
SET duration_days = 30, price_per_period = 29.99 
WHERE id = 'pricing_3';

-- 插入更多示例定价方案
INSERT IGNORE INTO agent_pricing_plans (id, agent_id, name, type, usage_count, price_per_usage, currency, description, is_active) VALUES
('pricing_4', 'agent_2', '开发者基础版', 'per_usage', 50, 0.15, 'CNY', '50次代码生成，适合个人开发者', true),
('pricing_5', 'agent_2', '开发者专业版', 'time_based', NULL, NULL, 'CNY', '7天无限代码生成', true),
('pricing_6', 'agent_3', '创作者套餐', 'per_usage', 200, 0.12, 'CNY', '200次创意写作，激发灵感', true);

-- 更新开发者专业版的时间计费字段
UPDATE agent_pricing_plans 
SET duration_days = 7, price_per_period = 19.99 
WHERE id = 'pricing_5';
