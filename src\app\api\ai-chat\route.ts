import { NextRequest, NextResponse } from 'next/server';
import { AIProvider } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, config, messages, content, files } = body;

    console.log('=== AI Chat API 请求 ===');
    console.log('Provider:', provider);
    console.log('Model:', config.model);
    console.log('Messages count:', messages?.length || 0);
    console.log('========================');

    switch (provider) {
      case 'openai':
        return await handleOpenAI(config, messages, content);
      case 'google':
        return await handleGoogle(config, messages, content);
      case 'anthropic':
        return await handleAnthropic(config, messages, content);
      case 'deepseek':
        return await handleDeepSeek(config, messages, content);
      default:
        return NextResponse.json(
          { success: false, error: `不支持的 AI 提供商: ${provider}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('AI Chat API 错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

async function handleOpenAI(config: any, messages: any[], content: string) {
  // 确保 baseUrl 包含 /v1
  let baseUrl = config.baseUrl || 'https://api.openai.com/v1';
  if (!baseUrl.includes('/v1')) {
    baseUrl = baseUrl.replace(/\/$/, '') + '/v1';
  }
  const url = `${baseUrl}/chat/completions`;
  
  const requestBody = {
    model: config.model,
    messages: messages,
    temperature: parseFloat(config.temperature) || 0.7,
    max_tokens: parseInt(config.maxTokens) || 4096,
  };

  console.log('OpenAI 请求:', { url, model: config.model });

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    console.error('OpenAI API 错误:', errorData);
    return NextResponse.json(
      { 
        success: false, 
        error: errorData.error?.message || `OpenAI API 错误: ${response.status}` 
      },
      { status: response.status }
    );
  }

  const data = await response.json();
  return NextResponse.json({
    success: true,
    data: {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
    },
  });
}

async function handleGoogle(config: any, messages: any[], content: string) {
  // 处理 baseUrl，避免路径重复
  let baseUrl = config.baseUrl || 'https://generativelanguage.googleapis.com';

  // 移除末尾的斜杠
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1);
  }

  // 智能处理 URL 路径，兼容标准 API 和代理 API
  let url;
  let isProxyAPI = false;

  if (baseUrl.includes('/v1beta')) {
    // 如果已经包含 v1beta 路径，直接添加 models 部分
    url = `${baseUrl}/models/${config.model}:generateContent?key=${config.apiKey}`;
  } else if (baseUrl.includes('/v1') && !baseUrl.includes('/v1beta')) {
    // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
    url = `${baseUrl}/chat/completions`;
    isProxyAPI = true;
  } else {
    // 标准情况，添加完整的 v1beta/models 路径
    url = `${baseUrl}/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`;
  }

  // 根据 API 类型选择不同的请求体格式
  let requestBody;
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    requestBody = {
      model: config.model,
      messages: messages.map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: parseFloat(config.temperature) || 0.7,
      max_tokens: parseInt(config.maxTokens) || 4096,
    };
    headers['Authorization'] = `Bearer ${config.apiKey}`;
  } else {
    // 标准 Google API 格式
    requestBody = {
      contents: messages.map((msg: any) => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      })),
      generationConfig: {
        temperature: parseFloat(config.temperature) || 0.7,
        maxOutputTokens: parseInt(config.maxTokens) || 4096,
      }
    };
  }

  console.log('Google 请求:', { url, model: config.model, isProxyAPI });

  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    console.error('Google API 错误:', errorData);
    return NextResponse.json(
      { 
        success: false, 
        error: errorData.error?.message || `Google API 错误: ${response.status}` 
      },
      { status: response.status }
    );
  }

  const data = await response.json();
  console.log('Google API 响应:', JSON.stringify(data, null, 2));

  // 根据 API 类型解析不同的响应格式
  let responseContent;
  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    responseContent = data.choices?.[0]?.message?.content || '';
  } else {
    // 标准 Google API 格式
    responseContent = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
  }

  console.log('提取的内容:', responseContent.substring(0, 200) + '...');
  return NextResponse.json({
    success: true,
    data: {
      content: responseContent,
      usage: data.usage,
    },
  });
}

async function handleAnthropic(config: any, messages: any[], content: string) {
  // 处理 baseUrl，兼容标准 API 和代理 API
  let baseUrl = config.baseUrl || 'https://api.anthropic.com';

  // 移除末尾的斜杠
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1);
  }

  // 智能处理 URL 路径，兼容标准 API 和代理 API
  let url;
  let isProxyAPI = false;

  if (baseUrl.includes('/v1') && !baseUrl.includes('/v1/messages')) {
    // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
    url = `${baseUrl}/chat/completions`;
    isProxyAPI = true;
  } else {
    // 标准 Anthropic API 格式
    url = `${baseUrl}/v1/messages`;
  }

  // 根据 API 类型选择不同的请求体格式
  let requestBody;
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${config.apiKey}`,
  };

  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    requestBody = {
      model: config.model,
      messages: messages,
      temperature: parseFloat(config.temperature) || 0.7,
      max_tokens: parseInt(config.maxTokens) || 4096,
    };
  } else {
    // 标准 Anthropic API 格式
    requestBody = {
      model: config.model,
      max_tokens: parseInt(config.maxTokens) || 4096,
      messages: messages,
      temperature: parseFloat(config.temperature) || 0.7,
    };
    headers['anthropic-version'] = '2023-06-01';
  }

  console.log('Anthropic 请求:', { url, model: config.model, isProxyAPI });

  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    console.error('Anthropic API 错误:', errorData);
    return NextResponse.json(
      { 
        success: false, 
        error: errorData.error?.message || `Anthropic API 错误: ${response.status}` 
      },
      { status: response.status }
    );
  }

  const data = await response.json();

  // 根据 API 类型解析不同的响应格式
  let responseContent;
  if (isProxyAPI) {
    // 代理 API 使用 OpenAI 兼容格式
    responseContent = data.choices?.[0]?.message?.content || '';
  } else {
    // 标准 Anthropic API 格式
    responseContent = data.content?.[0]?.text || '';
  }

  return NextResponse.json({
    success: true,
    data: {
      content: responseContent,
      usage: data.usage,
    },
  });
}

async function handleDeepSeek(config: any, messages: any[], content: string) {
  const url = `${config.baseUrl || 'https://api.deepseek.com'}/v1/chat/completions`;

  const requestBody = {
    model: config.model,
    messages: messages,
    temperature: parseFloat(config.temperature) || 0.7,
    max_tokens: parseInt(config.maxTokens) || 4096,
  };

  console.log('DeepSeek 请求:', { url, model: config.model });
  console.log('DeepSeek 请求体:', JSON.stringify(requestBody, null, 2));

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('DeepSeek API 错误 - 状态码:', response.status);
    console.error('DeepSeek API 错误 - 响应头:', Object.fromEntries(response.headers.entries()));
    console.error('DeepSeek API 错误 - 响应体:', errorText);

    let errorData: any = {};
    try {
      errorData = JSON.parse(errorText);
    } catch (e) {
      console.error('无法解析错误响应为JSON:', e);
    }

    return NextResponse.json(
      {
        success: false,
        error: errorData.error?.message || errorData.message || errorText || `DeepSeek API 错误: ${response.status}`
      },
      { status: response.status }
    );
  }

  const data = await response.json();
  return NextResponse.json({
    success: true,
    data: {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
    },
  });
}

