'use client';

import React, { memo, useMemo, useState } from 'react';
import { Flexbox } from 'react-layout-kit';
import { createStyles } from 'antd-style';
import { Button, Typography, Empty, App, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { MessageSquare, MoreHorizontal, Edit3, Trash2, DollarSign } from 'lucide-react';
import { useChatStore, DEFAULT_AGENT_ID } from '@/store/chat';
import { ChatSession } from '@/types';
import { agentStatusApi } from '@/lib/api';
import AgentPricingModal from './AgentPricingModal';

const { Text } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100%;
    background: ${token.colorBgContainer};
    border-left: 1px solid ${token.colorBorderSecondary};
    display: flex;
    flex-direction: column;
  `,
  
  header: css`
    padding: 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: ${token.colorText};
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .subtitle {
      color: ${token.colorTextSecondary};
      font-size: 12px;
      margin-top: 4px;
    }
  `,
  
  content: css`
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  `,
  
  sessionGroup: css`
    margin-bottom: 16px;
    
    .group-title {
      font-size: 12px;
      color: ${token.colorTextSecondary};
      margin-bottom: 8px;
      padding: 0 8px;
      font-weight: 500;
    }
  `,
  
  sessionItem: css`
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 4px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    
    &:hover {
      background: ${token.colorFillTertiary};
      border-color: ${token.colorBorderSecondary};
      
      .session-actions {
        opacity: 1;
      }
    }
    
    .session-title {
      font-size: 14px;
      color: ${token.colorText};
      margin-bottom: 4px;
      font-weight: 500;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .session-preview {
      font-size: 12px;
      color: ${token.colorTextSecondary};
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      margin-bottom: 6px;
    }
    
    .session-time {
      font-size: 11px;
      color: ${token.colorTextTertiary};
    }
    
    .session-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      opacity: 0;
      transition: opacity 0.2s ease;
      display: flex;
      gap: 4px;
    }
  `,
  
  emptyState: css`
    padding: 40px 20px;
    text-align: center;
    
    .empty-icon {
      font-size: 48px;
      color: ${token.colorTextQuaternary};
      margin-bottom: 16px;
    }
    
    .empty-title {
      color: ${token.colorTextSecondary};
      margin-bottom: 8px;
    }
    
    .empty-description {
      color: ${token.colorTextTertiary};
      font-size: 12px;
    }
  `,
}));

interface HistoryPanelProps {
  width?: number;
}

const HistoryPanel: React.FC<HistoryPanelProps> = memo(({ width = 280 }) => {
  const { styles } = useStyles();
  const { modal, message } = App.useApp();
  const [pricingModalVisible, setPricingModalVisible] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<{ id: string; name: string } | null>(null);

  const {
    historySessions,
    currentSessions,
    activeSessionId,
    switchToSession,
    deleteSession,
    updateSessionTitle
  } = useChatStore();

  // 获取当前活跃会话
  const currentSession = currentSessions.find(s => s.id === activeSessionId);

  // 按时间分组
  const groupedSessions = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const groups: { [key: string]: ChatSession[] } = {
      '今天': [],
      '昨天': [],
      '本周': [],
      '更早': [],
    };

    historySessions.forEach(session => {
      const sessionDate = new Date(session.updatedAt);
      
      if (sessionDate >= today) {
        groups['今天'].push(session);
      } else if (sessionDate >= yesterday) {
        groups['昨天'].push(session);
      } else if (sessionDate >= thisWeek) {
        groups['本周'].push(session);
      } else {
        groups['更早'].push(session);
      }
    });

    // 过滤掉空的分组
    return Object.entries(groups).filter(([_, sessions]) => sessions.length > 0);
  }, [historySessions]);

  // 获取会话显示标题
  const getSessionDisplayTitle = (session: ChatSession): string => {
    // 如果是智能体会话且有智能体名称，优先显示智能体名称
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID && session.agentName) {
      return session.agentName;
    }

    // 如果用户已经自定义了标题，显示自定义标题
    if (session.title && session.title !== '新会话' && session.title !== '新对话') {
      return session.title;
    }

    // 否则显示默认标题
    return session.title || '新会话';
  };

  const handleSessionClick = async (session: ChatSession) => {
    console.log('点击历史会话:', session.sessionUniqueId);
    console.log('当前历史会话数量:', historySessions.length);
    console.log('当前会话数量:', currentSessions.length);

    // 直接切换会话，不阻止用户访问
    switchToSession(session.sessionUniqueId);

    // 如果是智能体会话，检查状态并显示提示（但不阻止切换）
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID) {
      try {
        const result = await agentStatusApi.checkStatus(session.agentId);

        if (result.success && result.data) {
          if (!result.data.exists) {
            message.warning(`智能体"${result.data.name || '未知'}"已不存在，无法发送消息`);
          } else if (!result.data.enabled) {
            message.warning(`智能体"${result.data.name || '未知'}"已被禁用，无法发送消息`);
          }
        }
      } catch (error) {
        console.error('检查智能体状态失败:', error);
        // 静默处理错误，不影响用户体验
      }
    }
  };

  const handleRename = (session: ChatSession, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    const modalInstance = modal.confirm({
      title: '重命名会话',
      content: (
        <input
          id="session-title-input"
          defaultValue={getSessionDisplayTitle(session)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              const input = e.target as HTMLInputElement;
              const newTitle = input.value.trim();
              if (newTitle && newTitle !== session.title) {
                updateSessionTitle(session.sessionUniqueId, newTitle);
                message.success('会话重命名成功');
              }
              modalInstance.destroy();
            }
          }}
        />
      ),
      onOk: () => {
        const input = document.getElementById('session-title-input') as HTMLInputElement;
        const newTitle = input?.value.trim();
        if (newTitle && newTitle !== session.title) {
          updateSessionTitle(session.sessionUniqueId, newTitle);
          message.success('会话重命名成功');
        }
      },
      okText: '确认',
      cancelText: '取消',
    });

    // 自动聚焦输入框
    setTimeout(() => {
      const input = document.getElementById('session-title-input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 100);
  };

  const handleDelete = (session: ChatSession, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    modal.confirm({
      title: '删除会话',
      content: `确定要删除会话"${getSessionDisplayTitle(session)}"吗？此操作不可撤销。`,
      onOk: () => {
        deleteSession(session.sessionUniqueId);
        message.success('会话删除成功');
      },
      okText: '删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
    });
  };

  const handleShowPricing = (session: ChatSession, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    // 检查是否是智能体会话
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID) {
      setSelectedAgent({
        id: session.agentId,
        name: session.agentName || '智能体'
      });
      setPricingModalVisible(true);
    } else {
      message.info('当前会话不是智能体会话，无需查看定价信息');
    }
  };

  const handleMenuClick = (key: string, session: ChatSession, e?: any) => {
    switch (key) {
      case 'pricing':
        handleShowPricing(session, e);
        break;
      case 'rename':
        handleRename(session, e);
        break;
      case 'delete':
        handleDelete(session, e);
        break;
    }
  };

  const getSessionMenuItems = (session: ChatSession): MenuProps['items'] => {
    const items: MenuProps['items'] = [
      {
        key: 'rename',
        label: '重命名',
        icon: <Edit3 size={14} />,
      },
      {
        key: 'delete',
        label: '删除',
        icon: <Trash2 size={14} />,
        danger: true,
      },
    ];

    // 如果是智能体会话，添加定价选项
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID) {
      items.unshift({
        key: 'pricing',
        label: '定价',
        icon: <DollarSign size={14} />,
      });
    }

    return items;
  };

  const getSessionPreview = (session: ChatSession) => {
    const lastMessage = session.messages[session.messages.length - 1];
    if (!lastMessage) return '暂无消息';
    
    return lastMessage.role === 'user' 
      ? `你: ${lastMessage.content}` 
      : lastMessage.content;
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60 * 1000) return '刚刚';
    if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}分钟前`;
    if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
    
    return date.toLocaleDateString('zh-CN', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div className={styles.container} style={{ width }}>
      <div className={styles.header}>
        <div className="title">
          <MessageSquare size={16} />
          历史会话
        </div>
        <div className="subtitle">
          {historySessions.length} 个会话
        </div>
      </div>
      
      <div className={styles.content}>
        {groupedSessions.length === 0 ? (
          <div className={styles.emptyState}>
            <div className="empty-icon">💬</div>
            <div className="empty-title">暂无历史会话</div>
            <div className="empty-description">
              开始新的对话后，之前的会话将出现在这里
            </div>
          </div>
        ) : (
          groupedSessions.map(([groupName, sessions]) => (
            <div key={groupName} className={styles.sessionGroup}>
              <div className="group-title">{groupName}</div>
              {sessions.map(session => (
                <div
                  key={session.id}
                  className={styles.sessionItem}
                  onClick={() => handleSessionClick(session)}
                >
                  <div className="session-title">{getSessionDisplayTitle(session)}</div>
                  <div className="session-preview">
                    {getSessionPreview(session)}
                  </div>
                  <div className="session-time">
                    {formatTime(session.updatedAt)}
                  </div>
                  
                  <div className="session-actions">
                    <Dropdown
                      menu={{
                        items: getSessionMenuItems(session),
                        onClick: ({ key }) => handleMenuClick(key, session)
                      }}
                      trigger={['click']}
                      placement="bottomRight"
                    >
                      <Button
                        type="text"
                        size="small"
                        icon={<MoreHorizontal size={12} />}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Dropdown>
                  </div>
                </div>
              ))}
            </div>
          ))
        )}
      </div>

      {/* 定价信息模态框 */}
      {selectedAgent && (
        <AgentPricingModal
          visible={pricingModalVisible}
          onClose={() => {
            setPricingModalVisible(false);
            setSelectedAgent(null);
          }}
          agentId={selectedAgent.id}
          agentName={selectedAgent.name}
        />
      )}
    </div>
  );
});

HistoryPanel.displayName = 'HistoryPanel';

export default HistoryPanel;
