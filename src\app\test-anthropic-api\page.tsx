'use client';

import React, { useState } from 'react';
import { Button, Input, Card, Typography, Space, Alert, Divider, Select } from 'antd';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function TestAnthropicAPIPage() {
  const [apiKey, setApiKey] = useState('');
  const [baseUrl, setBaseUrl] = useState('https://api.anthropic.com');
  const [baseModel, setBaseModel] = useState('claude-sonnet-4');
  const [modelSuffix, setModelSuffix] = useState('20250514');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // 计算完整的模型名称
  const fullModel = modelSuffix ? `${baseModel}-${modelSuffix}` : baseModel;

  const presetUrls = [
    { label: '官方 API', value: 'https://api.anthropic.com' },
    { label: '代理 API (dpapi)', value: 'https://www.dpapi.top/v1' },
    { label: '自定义', value: 'custom' },
  ];

  const testConnection = async () => {
    if (!apiKey) {
      setResult({ success: false, error: '请输入 API Key' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/test-ai-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'anthropic',
          apiKey,
          baseUrl,
          model: fullModel,
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ 
        success: false, 
        error: error instanceof Error ? error.message : '测试失败' 
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Anthropic Claude API 连接测试</Title>
      
      <Alert
        message="API Key 和 URL 兼容性说明"
        description={
          <div>
            <Paragraph>
              <Text strong>API Key 格式：</Text>
            </Paragraph>
            <ul>
              <li><Text code>sk-ant-api03-...</Text> - 官方 Anthropic API Key</li>
              <li><Text code>sk-WVkavCN...</Text> - OpenAI 格式（某些代理 API 支持）</li>
            </ul>
            <Paragraph style={{ marginTop: 16 }}>
              <Text strong>URL 格式：</Text>
            </Paragraph>
            <ul>
              <li><Text code>https://api.anthropic.com</Text> - 官方 Anthropic API</li>
              <li><Text code>https://www.dpapi.top/v1</Text> - 代理 API（OpenAI 兼容格式）</li>
            </ul>
            <Paragraph style={{ marginTop: 16 }}>
              <Text strong>模型名称：</Text>
            </Paragraph>
            <ul>
              <li>选择基础模型后，可以编辑后缀来测试不同版本</li>
              <li>例如：<Text code>claude-sonnet-4-20250514</Text></li>
              <li>如果代理 API 不支持某个版本，可以尝试修改后缀</li>
            </ul>
            <Paragraph style={{ marginTop: 16 }}>
              代码会自动检测 URL 格式并使用相应的请求格式，避免路径重复问题。
            </Paragraph>
          </div>
        }
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Card title="测试配置">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Base URL:</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              value={baseUrl}
              onChange={(value) => {
                if (value !== 'custom') {
                  setBaseUrl(value);
                }
              }}
            >
              {presetUrls.map(url => (
                <Option key={url.value} value={url.value}>
                  {url.label}
                </Option>
              ))}
            </Select>
            {baseUrl === 'custom' && (
              <Input
                placeholder="输入自定义 Base URL"
                style={{ marginTop: 8 }}
                onChange={(e) => setBaseUrl(e.target.value)}
              />
            )}
          </div>

          <div>
            <Text strong>API Key:</Text>
            <Input.Password
              placeholder="输入你的 API Key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              style={{ marginTop: 8 }}
            />
          </div>
          
          <div>
            <Text strong>基础模型:</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              value={baseModel}
              onChange={(value) => {
                setBaseModel(value);
                // 根据选择的模型设置默认后缀
                if (value === 'claude-sonnet-4') {
                  setModelSuffix('20250514');
                } else if (value === 'claude-3-5-sonnet') {
                  setModelSuffix('20241022');
                } else if (value === 'claude-3-opus') {
                  setModelSuffix('20240229');
                } else if (value === 'claude-3-5-haiku') {
                  setModelSuffix('20241022');
                } else if (value === 'claude-3-haiku') {
                  setModelSuffix('20240307');
                } else {
                  setModelSuffix('');
                }
              }}
            >
              <Option value="claude-sonnet-4">Claude Sonnet 4 (最新)</Option>
              <Option value="claude-3-5-sonnet">Claude 3.5 Sonnet</Option>
              <Option value="claude-3-opus">Claude 3 Opus</Option>
              <Option value="claude-3-5-haiku">Claude 3.5 Haiku</Option>
              <Option value="claude-3-haiku">Claude 3 Haiku</Option>
              <Option value="claude-2.1">Claude 2.1</Option>
              <Option value="claude-2.0">Claude 2.0</Option>
              <Option value="claude-instant-1.2">Claude Instant 1.2</Option>
            </Select>
          </div>

          <div>
            <Text strong>模型后缀 (可编辑):</Text>
            <Input
              style={{ width: '100%', marginTop: 8 }}
              value={modelSuffix}
              onChange={(e) => setModelSuffix(e.target.value)}
              placeholder="例如: 20250514"
              addonBefore={baseModel + '-'}
            />
            <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: 4 }}>
              完整模型名称: <Text code>{fullModel}</Text>
            </Text>
          </div>

          <Button 
            type="primary" 
            onClick={testConnection}
            loading={loading}
            style={{ marginTop: 16 }}
          >
            测试连接
          </Button>
        </Space>
      </Card>

      {result && (
        <Card title="测试结果" style={{ marginTop: 24 }}>
          {result.success ? (
            <Alert
              message="连接成功！"
              description={result.data?.message || ''}
              type="success"
              showIcon
            />
          ) : (
            <Alert
              message="连接失败"
              description={result.error}
              type="error"
              showIcon
            />
          )}
          
          <Divider />
          
          <Text strong>完整响应:</Text>
          <TextArea
            value={JSON.stringify(result, null, 2)}
            rows={10}
            readOnly
            style={{ marginTop: 8, fontFamily: 'monospace' }}
          />
        </Card>
      )}

      <Card title="URL 构建逻辑" style={{ marginTop: 24 }}>
        <Space direction="vertical">
          <div>
            <Text strong>官方 API URL:</Text>
            <Paragraph>
              <Text code>https://api.anthropic.com</Text> → 
              <Text code>https://api.anthropic.com/v1/messages</Text>
            </Paragraph>
            <Text type="secondary">使用 Anthropic 原生格式的请求体和响应</Text>
          </div>
          
          <div>
            <Text strong>代理 API URL:</Text>
            <Paragraph>
              <Text code>https://www.dpapi.top/v1</Text> → 
              <Text code>https://www.dpapi.top/v1/chat/completions</Text>
            </Paragraph>
            <Text type="secondary">使用 OpenAI 兼容格式的请求体和响应</Text>
          </div>
          
          <div>
            <Text strong>自动检测逻辑:</Text>
            <ul style={{ marginTop: 8 }}>
              <li>如果 Base URL 包含 <Text code>/v1</Text> 但不包含 <Text code>/v1/messages</Text>，则认为是代理 API</li>
              <li>代理 API 使用 OpenAI 兼容的 <Text code>/chat/completions</Text> 端点</li>
              <li>代理 API 使用 <Text code>Authorization: Bearer</Text> 头部认证</li>
              <li>官方 API 使用 <Text code>/v1/messages</Text> 端点和 <Text code>anthropic-version</Text> 头部</li>
            </ul>
          </div>
        </Space>
      </Card>

      <Card title="常见问题" style={{ marginTop: 24 }}>
        <Space direction="vertical">
          <div>
            <Text strong>1. 路径重复错误</Text>
            <Paragraph>
              如果看到 <Text code>/v1/v1/messages</Text> 这样的错误，说明 URL 路径重复了。现在代码会自动处理这个问题。
            </Paragraph>
          </div>
          
          <div>
            <Text strong>2. API Key 格式</Text>
            <Paragraph>
              官方 Anthropic API 需要 <Text code>sk-ant-api03-</Text> 格式的 key，代理 API 可能接受 OpenAI 格式的 key。
            </Paragraph>
          </div>
          
          <div>
            <Text strong>3. 模型名称和版本</Text>
            <Paragraph>
              使用基础模型名称 + 版本后缀的格式，如：
              <ul style={{ marginTop: 8 }}>
                <li><Text code>claude-sonnet-4-20250514</Text> - Claude Sonnet 4 最新版本</li>
                <li><Text code>claude-3-5-sonnet-20241022</Text> - Claude 3.5 Sonnet</li>
              </ul>
              如果某个版本不可用，可以尝试修改后缀或移除后缀。
            </Paragraph>
          </div>
        </Space>
      </Card>
    </div>
  );
}
