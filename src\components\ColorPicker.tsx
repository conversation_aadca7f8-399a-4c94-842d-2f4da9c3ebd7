'use client';

import { ColorSwatches, PrimaryColors, NeutralColors, primaryColors, neutralColors } from '@lobehub/ui';
import { memo } from 'react';
import { Card, Typography, Space } from 'antd';

const { Title, Text } = Typography;

interface ColorPickerProps {
  onPrimaryColorChange?: (color: PrimaryColors) => void;
  onNeutralColorChange?: (color: NeutralColors) => void;
  primaryColor?: PrimaryColors;
  neutralColor?: NeutralColors;
}

const ColorPicker = memo<ColorPickerProps>(({
  onPrimaryColorChange,
  onNeutralColorChange,
  primaryColor = '',
  neutralColor = '',
}) => {
  const handlePrimaryColorSelect = (color: any) => {
    onPrimaryColorChange?.(color as PrimaryColors);
  };

  const handleNeutralColorSelect = (color: any) => {
    onNeutralColorChange?.(color as NeutralColors);
  };

  const primaryColorOptions = [
    {
      color: 'rgba(0, 0, 0, 0)',
      title: '默认',
    },
    {
      color: primaryColors.red,
      title: '红色',
    },
    {
      color: primaryColors.orange,
      title: '橙色',
    },
    {
      color: primaryColors.gold,
      title: '金色',
    },
    {
      color: primaryColors.yellow,
      title: '黄色',
    },
    {
      color: primaryColors.lime,
      title: '青柠',
    },
    {
      color: primaryColors.green,
      title: '绿色',
    },
    {
      color: primaryColors.cyan,
      title: '青色',
    },
    {
      color: primaryColors.blue,
      title: '蓝色',
    },
    {
      color: primaryColors.geekblue,
      title: '极客蓝',
    },
    {
      color: primaryColors.purple,
      title: '紫色',
    },
    {
      color: primaryColors.magenta,
      title: '品红',
    },
  ];

  const neutralColorOptions = [
    {
      color: 'rgba(0, 0, 0, 0)',
      title: '默认',
    },
    {
      color: neutralColors.red,
      title: '红色',
    },
    {
      color: neutralColors.orange,
      title: '橙色',
    },
    {
      color: neutralColors.yellow,
      title: '黄色',
    },
    {
      color: neutralColors.green,
      title: '绿色',
    },
    {
      color: neutralColors.blue,
      title: '蓝色',
    },
    {
      color: neutralColors.purple,
      title: '紫色',
    },
    {
      color: neutralColors.grey,
      title: '灰色',
    },
  ];

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Card>
        <Title level={4}>主色调</Title>
        <Text type="secondary">选择应用的主要颜色主题</Text>
        <div style={{ marginTop: 16 }}>
          <ColorSwatches
            colors={primaryColorOptions}
            onSelect={handlePrimaryColorSelect}
            value={primaryColor}
          />
        </div>
      </Card>

      <Card>
        <Title level={4}>中性色</Title>
        <Text type="secondary">选择应用的中性色调</Text>
        <div style={{ marginTop: 16 }}>
          <ColorSwatches
            colors={neutralColorOptions}
            onSelect={handleNeutralColorSelect}
            value={neutralColor}
          />
        </div>
      </Card>
    </Space>
  );
});

ColorPicker.displayName = 'ColorPicker';

export default ColorPicker;
